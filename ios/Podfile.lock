PODS:
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.4)
  - FBReactNativeSpec (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.4)
    - RCTTypeSafety (= 0.73.4)
    - React-Core (= 0.73.4)
    - React-jsi (= 0.73.4)
    - ReactCommon/turbomodule/core (= 0.73.4)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.73.4):
    - hermes-engine/Pre-built (= 0.73.4)
  - hermes-engine/Pre-built (0.73.4)
  - libevent (2.1.12)
  - lottie-ios (4.4.1)
  - lottie-react-native (6.7.2):
    - glog
    - lottie-ios (= 4.4.1)
    - RCT-<PERSON>olly (= 2022.05.16.00)
    - React-Core
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.4)
  - RCTTypeSafety (0.73.4):
    - FBLazyVector (= 0.73.4)
    - RCTRequired (= 0.73.4)
    - React-Core (= 0.73.4)
  - React (0.73.4):
    - React-Core (= 0.73.4)
    - React-Core/DevSupport (= 0.73.4)
    - React-Core/RCTWebSocket (= 0.73.4)
    - React-RCTActionSheet (= 0.73.4)
    - React-RCTAnimation (= 0.73.4)
    - React-RCTBlob (= 0.73.4)
    - React-RCTImage (= 0.73.4)
    - React-RCTLinking (= 0.73.4)
    - React-RCTNetwork (= 0.73.4)
    - React-RCTSettings (= 0.73.4)
    - React-RCTText (= 0.73.4)
    - React-RCTVibration (= 0.73.4)
  - React-callinvoker (0.73.4)
  - React-Codegen (0.73.4):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.4)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.4)
    - React-Core/RCTWebSocket (= 0.73.4)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.4)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.4)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.4)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.4)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.4):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.4)
    - React-debug (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-jsinspector (= 0.73.4)
    - React-logger (= 0.73.4)
    - React-perflogger (= 0.73.4)
    - React-runtimeexecutor (= 0.73.4)
  - React-debug (0.73.4)
  - React-Fabric (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.4)
    - React-Fabric/attributedstring (= 0.73.4)
    - React-Fabric/componentregistry (= 0.73.4)
    - React-Fabric/componentregistrynative (= 0.73.4)
    - React-Fabric/components (= 0.73.4)
    - React-Fabric/core (= 0.73.4)
    - React-Fabric/imagemanager (= 0.73.4)
    - React-Fabric/leakchecker (= 0.73.4)
    - React-Fabric/mounting (= 0.73.4)
    - React-Fabric/scheduler (= 0.73.4)
    - React-Fabric/telemetry (= 0.73.4)
    - React-Fabric/templateprocessor (= 0.73.4)
    - React-Fabric/textlayoutmanager (= 0.73.4)
    - React-Fabric/uimanager (= 0.73.4)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.4)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.4)
    - React-Fabric/components/modal (= 0.73.4)
    - React-Fabric/components/rncore (= 0.73.4)
    - React-Fabric/components/root (= 0.73.4)
    - React-Fabric/components/safeareaview (= 0.73.4)
    - React-Fabric/components/scrollview (= 0.73.4)
    - React-Fabric/components/text (= 0.73.4)
    - React-Fabric/components/textinput (= 0.73.4)
    - React-Fabric/components/unimplementedview (= 0.73.4)
    - React-Fabric/components/view (= 0.73.4)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.4)
    - RCTTypeSafety (= 0.73.4)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.4)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.4):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.4)
    - React-utils
  - React-hermes (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.4)
    - React-jsi
    - React-jsiexecutor (= 0.73.4)
    - React-jsinspector (= 0.73.4)
    - React-perflogger (= 0.73.4)
  - React-ImageManager (0.73.4):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.4):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.4):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-perflogger (= 0.73.4)
  - React-jsinspector (0.73.4)
  - React-logger (0.73.4):
    - glog
  - React-Mapbuffer (0.73.4):
    - glog
    - React-debug
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-document-picker (9.3.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-shake (5.6.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - React-nativeconfig (0.73.4)
  - React-NativeModulesApple (0.73.4):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.4)
  - React-RCTActionSheet (0.73.4):
    - React-Core/RCTActionSheetHeaders (= 0.73.4)
  - React-RCTAnimation (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.4):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.4):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.4):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.4)
  - React-RCTNetwork (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.4):
    - React-Core/RCTTextHeaders (= 0.73.4)
    - Yoga
  - React-RCTVibration (0.73.4):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.4)
  - React-runtimeexecutor (0.73.4):
    - React-jsi (= 0.73.4)
  - React-runtimescheduler (0.73.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.4):
    - React-logger (= 0.73.4)
    - ReactCommon/turbomodule (= 0.73.4)
  - ReactCommon/turbomodule (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.4)
    - React-cxxreact (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-logger (= 0.73.4)
    - React-perflogger (= 0.73.4)
    - ReactCommon/turbomodule/bridging (= 0.73.4)
    - ReactCommon/turbomodule/core (= 0.73.4)
  - ReactCommon/turbomodule/bridging (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.4)
    - React-cxxreact (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-logger (= 0.73.4)
    - React-perflogger (= 0.73.4)
  - ReactCommon/turbomodule/core (0.73.4):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.4)
    - React-cxxreact (= 0.73.4)
    - React-jsi (= 0.73.4)
    - React-logger (= 0.73.4)
    - React-perflogger (= 0.73.4)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.16.1):
    - React-Core
  - RNDateTimePicker (8.3.0):
    - React-Core
  - RNDeviceInfo (11.1.0):
    - React-Core
  - RNFileViewer (2.1.5):
    - React-Core
  - RNFitnessTracker (3.1.0):
    - React
  - RNFlashList (1.8.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.20.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNImageCropPicker (0.41.6):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.6)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.6):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNPermissions (4.1.5):
    - React-Core
  - RNReanimated (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
  - RNReanimated/reanimated (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
  - RNReanimated/reanimated/apple (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNReanimated/worklets (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.29.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNSVG (15.11.1):
    - React-Core
  - RNVectorIcons (10.2.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - SocketRocket (0.6.1)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - lottie-ios (from `../node_modules/lottie-ios`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-shake (from `../node_modules/react-native-shake`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFileViewer (from `../node_modules/react-native-file-viewer`)
  - "RNFitnessTracker (from `../node_modules/@kilohealth/rn-fitness-tracker`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - fmt
    - libevent
    - SocketRocket
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-11-17-RNv0.73.0-21043a3fc062be445e56a2c10ecd8be028dd9cc5
  lottie-ios:
    :path: "../node_modules/lottie-ios"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-shake:
    :path: "../node_modules/react-native-shake"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFileViewer:
    :path: "../node_modules/react-native-file-viewer"
  RNFitnessTracker:
    :path: "../node_modules/@kilohealth/rn-fitness-tracker"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: 84f6edbe225f38aebd9deaf1540a4160b1f087d7
  FBReactNativeSpec: d0086a479be91c44ce4687a962956a352d2dc697
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  hermes-engine: b2669ce35fc4ac14f523b307aff8896799829fe2
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  lottie-react-native: 3444859381572e468f1b3a57cc5728968af868a6
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: ab7f915c15569f04a49669e573e6e319a53f9faa
  RCTTypeSafety: 63b97ced7b766865057e7154db0e81ce4ee6cf1e
  React: 1c87497e50fa40ba9c54e5ea5e53483a0f8eecc0
  React-callinvoker: e3a52a9a93e3eb004d7282c26a4fb27003273fe6
  React-Codegen: 6d326180cf9c4bf272f3c07ffc014df7c3e6f501
  React-Core: d0ecde72894b792cb8922efaa0990199cbe85169
  React-CoreModules: 2ff1684dd517f0c441495d90a704d499f05e9d0a
  React-cxxreact: d9be2fac926741052395da0a6d0bab8d71e2f297
  React-debug: dfef758b88be3124182fb7f9b70f009cd3598feb
  React-Fabric: 4004cd4af7eca7b4074d1b4f6709861869b57885
  React-FabricImage: 389dfe3111a50ac9ada46063532134b0fca72604
  React-graphics: 461a34ad990db297940fb7989bb0c246dff1c088
  React-hermes: b9ac2f7b0c1eeb206eb883583cab7a973d570a6e
  React-ImageManager: 92cb6923e53e7a50864c78de714aabc1e1419cbf
  React-jserrorhandler: 577ced2d6a0b5280057845de68b4147e2bc3f31d
  React-jsi: 380cd24dd81a705dd042c18989fb10b07182210c
  React-jsiexecutor: 8ed7a18b9f119440efdcd424c8257dc7e18067e2
  React-jsinspector: 9ac353eccf6ab54d1e0a33862ba91221d1e88460
  React-logger: 0a57b68dd2aec7ff738195f081f0520724b35dab
  React-Mapbuffer: 114be72666d7e3e317c1a36fb566cd3ad712fcc8
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-document-picker: 4a7664812c7a25b02697f597c3d935360d454a2c
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-shake: 619d8d3bd9fe6dc7e8d9104fb37acebc6f6c1277
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  React-nativeconfig: ca8b90c736cf3be019cb332ca42d93dd95b32e05
  React-NativeModulesApple: 1fdffcce7772e274baeab33a1900f45feba86cbd
  React-perflogger: 8a1e1af5733004bdd91258dcefbde21e0d1faccd
  React-RCTActionSheet: 64bbff3a3963664c2d0146f870fe8e0264aee4c4
  React-RCTAnimation: b698168a7269265a4694727196484342d695f0c1
  React-RCTAppDelegate: dcd8e955116eb1d1908dfaf08b4c970812e6a1e6
  React-RCTBlob: 47f8c3b2b4b7fa2c5f19c43f0b7f77f57fb9d953
  React-RCTFabric: 1885187a31ad82c7270ea47f9ecd087a1df7ee3f
  React-RCTImage: ac0e77a44c290b20db783649b2b9cddc93e3eb99
  React-RCTLinking: e626fd2900913fe5d25922ea1be394b7aafa09c9
  React-RCTNetwork: d3114bce3977dafe8bd06421b29812f5a8527ba0
  React-RCTSettings: a53511f90d8df637a1a11ac729179a4d2f734481
  React-RCTText: f0176f5f5952f9a4a2c7354f5ae71f7c420aaf34
  React-RCTVibration: 8160223c6eda5b187079fec204f80eca8b8f3177
  React-rendererdebug: 9a0563e113d2c80060eaef92063e259051328d2b
  React-rncore: 1b3de0a735e73765a2920fb28748a83e8c81decb
  React-runtimeexecutor: e6ab6bb083dbdbdd489cff426ed0bce0652e6edf
  React-runtimescheduler: 1c054b58fef2ce74cdcbdcd70db190e10f56a617
  React-utils: 21a798438d45e70ed9c2e2fe0894ee32ba7b7c5b
  ReactCommon: dcc65c813041388dead6c8b477444757425ce961
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNCClipboard: d7a8903d32692b0a1e71738c3825465a9acae10e
  RNDateTimePicker: a793ed8822283f576dd0a205a0916c5098c2611f
  RNDeviceInfo: b899ce37a403a4dea52b7cb85e16e49c04a5b88e
  RNFileViewer: ce7ca3ac370e18554d35d6355cffd7c30437c592
  RNFitnessTracker: 3d80a66cbb25525ec0f92e8bdb1bf506837577d4
  RNFlashList: 0ac23f5209d8abeb6bebd5efcaf715a57dad48cd
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 98f7c55cc6105e9cfa5a0558d799fb6139d4fb54
  RNImageCropPicker: 8e39c01f205e00d739c31e682f068aac315587bf
  RNPermissions: 13186aab70330f78ab2b26c4b96f68099c5a3768
  RNReanimated: 58404dc83b0219846d04cafe62deadb4263829bb
  RNScreens: 2b73f5eb2ac5d94fbd61fa4be0bfebd345716825
  RNSVG: d23fa135b1fa2d61aed871ce0774fc5da707d4a1
  RNVectorIcons: bc3cd26c90d52e5d0fc1dc1302308e5e2b3402a5
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 64cd2a583ead952b0315d5135bf39e053ae9be70

PODFILE CHECKSUM: e4ae37b8bacb527a777236f8687c2b55ec0e2633

COCOAPODS: 1.16.2
