import {useCallback} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';
import DocumentServices from '../../services/DocumentServices';
import {setDocuments} from '../../store/DocumentsSlice';

const useDocuments = () => {
  const {user} = useAppSelector(state => state.user);
  const isClient = user.userType === 'client';
  const dispatch = useAppDispatch();

  const fetchDocuments = useCallback(async () => {
    try {
      if (isClient && user?.coach?._id) {
        await DocumentServices.GetDocumentOfCoach(user?.coach?._id).then(
          resp => {
            dispatch(setDocuments(resp.data));
          },
        );
      } else {
        await DocumentServices.fetchDocuments().then(resp => {
          dispatch(setDocuments(resp.data));
        });
      }
    } catch (error) {
      console.log('error fetching document', error);
    }
  }, [user._id, user.coach?._id]);

  return {
    fetchDocuments,
  };
};

export default useDocuments;
