import {useCallback} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';
import {setWaterIntake, setWeights} from '../../store/StatisticsSlice';
import WeightServices from '../../services/WeightServices';
import moment from 'moment';
import WaterService from '../../services/WaterService';
import {AxiosError} from 'axios';

const useStatistics = () => {
  const {user} = useAppSelector(state => state.user);
  const {waterIntake, weights} = useAppSelector(state => state.statistics);
  const dispatch = useAppDispatch();

  const fetchWeights = useCallback(async () => {
    try {
      const resp = await WeightServices.GetWeightByClient(user._id);
      if (resp.status === 200) {
        dispatch(setWeights(resp.data));
      }
    } catch (error) {
      console.error(error);
    }
  }, [user._id]);

  const fetchWaterIntake = useCallback(async () => {
    const today = moment().format('YYYY-MM-DD');

    await WaterService.GetWaterByDate(today)
      .then(resp => {
        dispatch(setWaterIntake(resp.data[0]));
      })
      .catch((error: AxiosError) => {
        console.log('CLIENT > GET WATER INTAKE -> ', error);
      });
  }, [user._id]);
  return {
    waterIntake,
    weights,
    fetchWeights,
    fetchWaterIntake,
  };
};

export default useStatistics;
