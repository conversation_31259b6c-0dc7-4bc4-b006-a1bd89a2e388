import {useCallback} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';
import MealServices from '../../services/MealServices';
import {AxiosError} from 'axios';
import {
  setMealPostsAction,
  updateLoadingStatus,
} from '../../store/mealPostSlice';

const usePosts = () => {
  const {user} = useAppSelector(state => state.user);
  const {mealPosts, isLoading} = useAppSelector(state => state.mealPosts);
  const dispatch = useAppDispatch();

  const fetchPosts = useCallback(async () => {
    dispatch(updateLoadingStatus(true));

    await MealServices.GetMealPostsForCoach(user._id)
      .then(response => {
        dispatch(setMealPostsAction(response.data?.mealsData));
        dispatch(updateLoadingStatus(false));
      })
      .catch((error: AxiosError) => {
        console.log('COACH > FETCH MEAL POSTS -> ', error.response?.data);
      });
  }, [user._id]);

  return {
    posts: mealPosts,
    isLoading,
    fetchPosts,
  };
};

export default usePosts;
