import {useCallback, useState} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';
import moment, {Moment} from 'moment';
import MealServices from '../../services/MealServices';
import {IFoodMeal, IMeal, IMealDetails} from '../../interfaces/IMeal';
import {addNewMeal, setDailyMeals, updateMeal} from '../../store/MealsSlice';
import {AxiosError, AxiosResponse} from 'axios';
import useApiHandler from '../../utilities/useApiHandler';
import {useTranslation} from 'react-i18next';
import {IFood} from '../../interfaces/IFoods';

const useMeals = () => {
  const {t} = useTranslation();
  const {user} = useAppSelector(state => state.user);
  const {meals} = useAppSelector(state => state.meals);
  const foodsStore = useAppSelector(state => state.foods);

  const [mealDetails, setMealDetails] = useState<IMealDetails>();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useAppDispatch();
  const {showErrorToast, handleAxiosErrors} = useApiHandler();

  const fetchMeals = useCallback(
    async (date: Moment) => {
      const today = date.format('YYYY-MM-DD');

      try {
        setIsLoading(true);
        const resp = await MealServices.GetMealsByClient(user._id, today);

        // SUCCESS GET
        if (resp.status === 200) {
          let newfoods = [] as Array<IMeal>;
          resp.data.forEach((food: IMeal) => {
            if (meals.includes(food) === false) newfoods.push(food);
          });

          dispatch(setDailyMeals(newfoods as Array<IMeal>));
        }
      } catch (error) {
        console.error(error);
        const err = error as AxiosError;
        console.log('error fetching meals', err);
      } finally {
        setIsLoading(false);
      }
    },
    [user._id],
  );

  // GET FOODS
  async function getMealDetails(mealId: string) {
    try {
      setIsLoading(true);
      const resp: AxiosResponse<IMealDetails> =
        await MealServices.GetMealDetails(mealId);

      resp.data;
      setMealDetails(resp.data);
      return resp.data;
    } catch (error) {
      console.log('-- get meal details  --', error);
      showErrorToast(t('Something went wrong in creating meal'));
      throw error;
    } finally {
      setIsLoading(false);
    }
  }

  async function createMeal(mealTitle: string) {
    try {
      setIsSubmitting(true);
      const resp: AxiosResponse<IMealDetails> = await MealServices.CreateMeal({
        mealTitle: mealTitle,
        mealFoods: [],
      });

      // SUCCESS CREATE
      if (resp.status === 201) {
        setMealDetails(resp.data);
        dispatch(addNewMeal(resp.data));
      }

      return resp.data;
    } catch (error) {
      const err = error as AxiosError;
      // INTERNET NOT WORKING
      if (err.code === 'ERR_NETWORK') {
        showErrorToast(t('Make sure your internet is working.'));
      } else {
        showErrorToast(t('Something went wrong in creating meal'));
      }
      throw error;
    } finally {
      setIsSubmitting(true);
    }
  }

  // ADD FOOD TO THE MEAL
  async function updateMealFoods(meal: IMealDetails, foodsArr: IFood[]) {
    const mealFoodsPayload = foodsArr.map(item => {
      return {foodId: item._id, serving: '1'} as IFoodMeal;
    }) as IFoodMeal[];
    const payload = {
      mealTitle: meal?.mealTitle,
      mealFoods: mealFoodsPayload,
    };

    try {
      const resp: AxiosResponse<IMealDetails> = await MealServices.UpdateMeal(
        meal?._id as string,
        payload,
      );

      // SUCCESS UPDATE
      if (resp.status === 200) {
        dispatch(updateMeal(resp.data));
      }

      return resp.data;
    } catch (error) {
      const err = error as AxiosError;
      console.log('- UPDATE MEAL Error -', err.response?.data);
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    }
  }

  return {
    meals,
    mealDetails,
    isSubmitting,
    fetchMeals,
    createMeal,
    isLoading,
    getMealDetails,
    updateMealFoods,
  };
};

export default useMeals;
