import {useCallback} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';
import ClientAuthServices from '../../services/ClientAuthServices';
import {
  setCoachInfo,
  setDailyGoal,
  setDailyNutrients,
  updateUser,
} from '../../store/userSlice';
import {IGoal} from '../../interfaces/IUser.';
import ClientServices from '../../services/ClientServices';
import {AxiosError} from 'axios';
import {
  deleteClient,
  setArchivedClients,
  setClients,
  toggleArchiveClient,
  updateClient,
  updateLoadingStatus,
} from '../../store/clientsSlice';
import {useTranslation} from 'react-i18next';
import Toast from 'react-native-toast-message';

const useClients = () => {
  const {user, dailyIntakeGoal, dailyMacroNutrients} = useAppSelector(
    state => state.user,
  );
  const {clients, count, isLoading, archivedClients} = useAppSelector(
    state => state.clients,
  );
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const fetchClient = async () => {
    try {
      const resp = await ClientAuthServices.GetClientById(user._id);

      // SUCCESS GET
      if (resp.status === 200) {
        if (resp.data?.goal) {
          dispatch(setDailyGoal(resp.data?.goal as IGoal));
          // console.log(resp.data?.goal, 'resp.data?.goal');
        }
        if (resp.data?.usedData) {
          dispatch(setDailyNutrients(resp.data?.usedData));
        }
        if (resp.data?.client?.coach) {
          dispatch(setCoachInfo(resp.data?.client?.coach));
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const fetchClientsForCoach = useCallback(
    async (sortKey: string, searchKey: string) => {
      try {
        dispatch(updateLoadingStatus(true));
        await ClientServices.FetchCoachClients(user._id, {
          sortKey:
            sortKey === 'name'
              ? 'name'
              : sortKey === 'new' || sortKey === 'old'
              ? 'createdAt'
              : '',
          searchKey: searchKey,
          sortOrder: sortKey === 'new' ? 'desc' : 'asc',
        })
          .then(response => {
            dispatch(
              setClients({
                clients: response.data.data,
                count: response.data.count,
              }),
            );
            dispatch(updateLoadingStatus(false));
          })
          .catch((error: AxiosError) => {
            console.log('COACH > FETCH CLIENTS -> ', error);
          });
      } catch (error) {
        console.error(error);
      }
    },
    [user._id],
  );

  const handleDeleteClient = async (clientID: string) => {
    try {
      dispatch(deleteClient({_id: clientID}));
      const resp = await ClientServices.DeleteClient(clientID);
      if ((resp.status = 200)) {
        Toast.show({
          type: 'success',
          text2: t('Client deleted'),
        });
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error deleting client', err);
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Error while deleting the client'),
      });
    }
  };

  const handleArchiveClient = async (id: string, status: boolean) => {
    try {
      dispatch(toggleArchiveClient({_id: id}));

      await ClientServices.ArchiveClient(id, status);
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error archiving client', err);
    }
  };
  const fetchArchivedClients = async (clientId: string) => {
    try {
      const resp = await ClientServices.GetArchivedClient(clientId);
      if (resp.status == 200) {
        dispatch(setArchivedClients({archivedclients: resp.data.clients}));
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error fetching archived clients', err);
    }
  };

  return {
    client: user,
    dailyIntakeGoal,
    dailyMacroNutrients,
    fetchClient,
    fetchClientsForCoach,
    clients,
    count,
    isLoading,
    handleDeleteClient,
    handleArchiveClient,
    fetchArchivedClients,
    archivedClients,
  };
};

export default useClients;
