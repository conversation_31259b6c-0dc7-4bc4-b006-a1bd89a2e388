import {useEffect} from 'react';
import {useAppSelector} from '../../store';
import useClients from '../models/useClients';
import useMeals from '../models/useMeals';
import useStatistics from '../models/useStatistics';
import useDocuments from '../models/useDocuments';
import useChats from '../models/useChats';
import usePosts from '../models/usePosts';
import SplashScreen from 'react-native-splash-screen';
import moment from 'moment';
import useActivity from '../models/useActivity';

const useFetchers = () => {
  const {_id, userType} = useAppSelector(state => state.user.user);

  const {fetchClient, fetchClientsForCoach, fetchArchivedClients} =
    useClients();
  const {fetchMeals} = useMeals();
  const {fetchWaterIntake, fetchWeights} = useStatistics();
  const {fetchDocuments} = useDocuments();
  const {fetchChats} = useChats();
  const {fetchPosts} = usePosts();
  const {fetchDailyActivity} = useActivity();

  useEffect(() => {
    if (!_id) SplashScreen.hide();
    !!_id &&
      userType === 'client' &&
      Promise.all([
        fetchClient(),
        fetchMeals(moment()),
        fetchWaterIntake(),
        fetchWeights(),
        fetchChats(),
        fetchDocuments(),
        fetchDailyActivity(_id, new Date()),
      ])
        .then(() => {
          SplashScreen.hide();
        })
        .catch(err => {
          SplashScreen.hide();
          console.log('Error in client fetcher', {err});
        });
    !!_id &&
      userType === 'coach' &&
      Promise.all([
        fetchPosts(),
        fetchClientsForCoach('', ''),
        fetchDocuments(),
        fetchChats(),
        fetchArchivedClients(_id),
      ])
        .then(() => {
          SplashScreen.hide();
        })
        .catch(err => {
          console.log('Error in coach fetcher', {err});
          SplashScreen.hide();
        });
  }, [_id]);
};

export default useFetchers;
