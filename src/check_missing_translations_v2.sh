#!/bin/bash

# Improved script to find missing translation keys
echo "=== IMPROVED MISSING TRANSLATION KEYS ANALYSIS ==="
echo "=================================================="

# Create output files
MISSING_KEYS_FILE="src/missing_translation_keys_v2.txt"
ALL_T_CALLS_FILE="src/all_t_function_calls_v2.txt"
ANALYSIS_REPORT="src/translation_analysis_report_v2.txt"

# Clear previous results
> "$MISSING_KEYS_FILE"
> "$ALL_T_CALLS_FILE"
> "$ANALYSIS_REPORT"

echo "Step 1: Extracting all t() function calls..."

# Find all t() function calls in TypeScript/JavaScript files using a more robust approach
find . -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.jsx" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -ho "t(['\"][^'\"]*['\"])" | \
sed "s/t(['\"]//g; s/['\"])//g" | \
sort | uniq > "$ALL_T_CALLS_FILE"

echo "Step 2: Checking against English translation file..."

# Check which keys are missing from en.json
while IFS= read -r key; do
    if [ ! -z "$key" ]; then
        # Use grep with fixed strings to avoid regex issues
        if ! grep -Fq "\"$key\":" src/i18n/locales/en.json; then
            echo "$key" >> "$MISSING_KEYS_FILE"
        fi
    fi
done < "$ALL_T_CALLS_FILE"

echo "Step 3: Generating analysis report..."

# Generate analysis report
{
    echo "=== IMPROVED TRANSLATION ANALYSIS REPORT ==="
    echo "Generated on: $(date)"
    echo ""
    echo "SUMMARY:"
    echo "--------"
    echo "Total t() function calls found: $(wc -l < "$ALL_T_CALLS_FILE")"
    echo "Missing translation keys: $(wc -l < "$MISSING_KEYS_FILE")"
    echo ""
    echo "MISSING KEYS:"
    echo "-------------"
    cat "$MISSING_KEYS_FILE"
    echo ""
    echo "ALL T() FUNCTION CALLS:"
    echo "----------------------"
    head -20 "$ALL_T_CALLS_FILE"
    echo "... (showing first 20 entries)"
} > "$ANALYSIS_REPORT"

echo ""
echo "Analysis complete!"
echo "Results saved to:"
echo "  - Missing keys: $MISSING_KEYS_FILE"
echo "  - All t() calls: $ALL_T_CALLS_FILE"
echo "  - Full report: $ANALYSIS_REPORT"
echo ""
echo "Missing translation keys: $(wc -l < "$MISSING_KEYS_FILE")"
