#!/bin/bash

# Script to find hard-coded strings that should be wrapped in t() function
echo "=== HARD-CODED STRINGS ANALYSIS ==="
echo "=================================="

# Create output files
HARDCODED_STRINGS="src/hardcoded_strings.txt"
HARDCODED_REPORT="src/hardcoded_strings_report.txt"

# Clear previous results
> "$HARDCODED_STRINGS"
> "$HARDCODED_REPORT"

echo "Step 1: Analyzing result.txt for hard-coded strings..."

# Extract lines from result.txt that show hard-coded strings (not using t() function)
# Look for patterns like: "Some text" or 'Some text' that are not wrapped in t()
grep -E "(Text|text|label|title|message|error|success|warning|info|placeholder|hint)" src/result.txt | \
grep -v "t(" | \
grep -E "['\"]\s*[A-Z][^'\"]*['\"]\s*[,;)]" | \
head -50 > temp_hardcoded.txt

echo "Step 2: Finding common UI text patterns..."

# Find common patterns that should be translated
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "(placeholder=|title=|label=|aria-label=)['\"][^'\"]*['\"]" | \
grep -v "t(" | \
head -20 >> temp_hardcoded.txt

echo "Step 3: Finding validation error messages..."

# Find validation error messages that might not be translated
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "(error|Error|required|Required|invalid|Invalid)" | \
grep -E "['\"]\s*[A-Z][^'\"]*['\"]\s*[,;)]" | \
grep -v "t(" | \
head -20 >> temp_hardcoded.txt

echo "Step 4: Finding toast/alert messages..."

# Find toast, alert, or notification messages
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "(toast|alert|notification|message)" | \
grep -E "['\"]\s*[A-Z][^'\"]*['\"]\s*[,;)]" | \
grep -v "t(" | \
head -20 >> temp_hardcoded.txt

echo "Step 5: Generating report..."

# Remove duplicates and generate report
sort temp_hardcoded.txt | uniq > "$HARDCODED_STRINGS"

{
    echo "=== HARD-CODED STRINGS ANALYSIS REPORT ==="
    echo "Generated on: $(date)"
    echo ""
    echo "SUMMARY:"
    echo "--------"
    echo "Potential hard-coded strings found: $(wc -l < "$HARDCODED_STRINGS")"
    echo ""
    echo "HARD-CODED STRINGS REQUIRING t() WRAPPER:"
    echo "----------------------------------------"
    cat "$HARDCODED_STRINGS"
    echo ""
    echo "RECOMMENDATIONS:"
    echo "---------------"
    echo "1. Review each string to determine if it should be translatable"
    echo "2. Wrap user-facing strings with t() function"
    echo "3. Add corresponding translation keys to all locale files"
    echo "4. Test the application to ensure translations work correctly"
} > "$HARDCODED_REPORT"

# Clean up
rm -f temp_hardcoded.txt

echo ""
echo "Analysis complete!"
echo "Results saved to:"
echo "  - Hard-coded strings: $HARDCODED_STRINGS"
echo "  - Analysis report: $HARDCODED_REPORT"
echo ""
echo "Potential hard-coded strings found: $(wc -l < "$HARDCODED_STRINGS")"
