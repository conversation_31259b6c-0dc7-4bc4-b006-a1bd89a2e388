import {
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {Button as RnButton} from '@rneui/themed';
import {Fonts, theme} from '../../utilities/theme';

interface Props {
  isShadow?: boolean;
  title?: string;
  containerStyle?: ViewStyle;
  onPress?: () => void;
  color?: string;
  titleStyle?: TextStyle;
  loading?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftIconPress?: () => void;
  onRightIconPress?: () => void;
  leftIconContainer?: ViewStyle;
  rightIconContainer?: ViewStyle;
}
const Button: React.FC<Props> = ({
  isShadow,
  title,
  containerStyle,
  onPress,
  color,
  titleStyle,
  loading,
  disabled,
  leftIcon,
  rightIcon,
  onLeftIconPress,
  onRightIconPress,
  leftIconContainer,
  rightIconContainer,
}) => {
  return (
    // <View style={[styles.shadow,]}>
    <RnButton
      containerStyle={[]}
      color={color}
      onPress={onPress}
      titleStyle={[styles.titleStyles, titleStyle]}
      buttonStyle={[styles.container, containerStyle]}
      loading={loading}
      disabled={disabled}
      disabledStyle={{backgroundColor: theme.lightColors?.grey3}}
      disabledTitleStyle={{color: '#8E8E8E'}}
      loadingProps={{color: theme.lightColors?.secondary}}>
      {/* LEFT ICON */}
      <TouchableOpacity
        disabled
        style={leftIconContainer}
        onPress={onLeftIconPress}>
        {leftIcon}
      </TouchableOpacity>
      {/* TITLE */}
      {title}
      {/* RIGHTBUTTON */}
      <TouchableOpacity disabled style={rightIconContainer}>
        {rightIcon}
      </TouchableOpacity>
    </RnButton>
    // </View>
  );
};

export default Button;

const styles = StyleSheet.create({
  container: {
    height: 45,
    borderRadius: 10,
    backgroundColor: theme.lightColors?.primary,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,

    elevation: 4,
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,

    elevation: 4,
    backgroundColor: 'white',
    borderRadius: 11,
  },
  titleStyles: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.white,
  },
});
