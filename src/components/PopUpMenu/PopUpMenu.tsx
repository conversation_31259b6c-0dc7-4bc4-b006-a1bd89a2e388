import {StyleSheet, Text, TextStyle, View} from 'react-native';
import React from 'react';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from 'react-native-popup-menu';
import {VerticalDots} from '../../assets/svgIcons';
import TextButtonwithIcon from '../ButtonwithIcon/ButtonwithIcon';
import {Fonts, theme} from '../../utilities/theme';

interface Option {
  id: number;
  label: string;
  onPress?: () => void;
  style?: TextStyle;
}
interface Props {
  opened: boolean;
  setOpened: (opened: boolean) => void;
  data: Option[];
}

const PopUpMenu: React.FC<Props> = ({opened, setOpened, data}) => {
  const handleOptionPress = (onPress?: () => void) => {
    if (onPress) {
      onPress();
    }
    setOpened(false);
  };
  return (
    <View>
      <Menu opened={opened} onBackdropPress={() => setOpened(false)}>
        <MenuTrigger>
          <TextButtonwithIcon
            leftIcon={<VerticalDots />}
            onPress={() => setOpened(true)}
            hitSlop={styles.hitSlop}
          />
        </MenuTrigger>
        <MenuOptions
          customStyles={{
            optionsContainer: [styles.customStyles],
          }}>
          {data?.map((item, index) => {
            return (
              <View key={item.id}>
                <MenuOption
                  key={item.id}
                  onSelect={() => handleOptionPress(item.onPress)}>
                  <View style={styles.menuActions}>
                    <Text style={[styles.menuLabel, item.style]}>
                      {item.label}
                    </Text>
                  </View>
                </MenuOption>
                {index !== data.length - 1 && (
                  <View style={[styles.menuDivider]} />
                )}
              </View>
            );
          })}
        </MenuOptions>
      </Menu>
    </View>
  );
};

export default PopUpMenu;

const styles = StyleSheet.create({
  customStyles: {
    backgroundColor: theme.lightColors?.background,
    borderRadius: 8,
    width: 100,
    justifyContent: 'center',
    marginTop: 10,
    marginLeft: -10,
  },
  menuActions: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    alignSelf: 'center',
  },
  menuDivider: {
    height: 1,
    backgroundColor: theme.lightColors?.grey2,
    width: '100%',
    alignSelf: 'center',
  },
  menuLabel: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    marginVertical: 8,
  },
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
});
