import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {Camera} from '../../assets/svgIcons';
import {theme} from '../../utilities/theme';
import PNGIcons from '../../assets/pngIcons';

interface Props {
  uri: string;
  onPress: () => void;
  showPickImage?: boolean;
}

const Avatar: React.FC<Props> = ({uri, onPress, showPickImage}) => {
  return (
    <View style={styles.avatarContainer}>
      <View style={styles.avatarContainerInner}>
        <Image
          source={uri ? {uri: uri} : PNGIcons.User}
          style={styles.imageContainer}
        />
        {showPickImage && (
          <TouchableOpacity
            style={styles.cameraIconContainer}
            onPress={onPress}>
            <Camera />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default Avatar;

const styles = StyleSheet.create({
  avatarContainer: {
    width: 100,
    height: 100,
    alignSelf: 'center',
    borderRadius: 100 / 2,
  },
  avatarContainerInner: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 100 / 2,
  },
  imageContainer: {
    width: 100,
    height: 100,
    borderRadius: 100 / 2,
    backgroundColor: theme.lightColors?.primary,
  },
  cameraIconContainer: {
    position: 'absolute',
    padding: 5,
    backgroundColor: theme.lightColors?.primary,
    borderRadius: 20,
    bottom: -2,
    right: -2,
    borderWidth: 2,
    borderColor: theme.lightColors?.background,
  },
  cameraIcon: {
    backgroundColor: theme.lightColors?.primary,
    width: 50,
    height: 50,
  },
});
