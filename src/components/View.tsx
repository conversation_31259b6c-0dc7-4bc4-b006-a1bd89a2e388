import {View as RNView, ViewProps as RNViewProps} from 'react-native';
import React from 'react';
import {makeStyles} from '@rneui/themed';

export interface ViewProps extends RNViewProps {
  fullWidth?: boolean;
}

export default function ({
  style,
  children,
  fullWidth,
  ...restProps
}: ViewProps) {
  const styles = useStyles({fullWidth});
  return (
    <RNView style={[styles.container, style]} {...restProps}>
      {children}
    </RNView>
  );
}

type Props = {
  fullWidth?: boolean;
};

const useStyles = makeStyles((theme, props: Props) => ({
  container: {
    backgroundColor: theme.colors.background,
    width: props.fullWidth ? '100%' : 'auto',
  },
}));
