import {
  ImageSourcePropType,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useMemo} from 'react';
import {Image} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';
import {useNavigation} from '@react-navigation/native';
import {useAppSelector} from '../../store';
import {useTranslation} from 'react-i18next';
import {Moment} from 'moment';
interface props {
  value: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snacks';
  image: ImageSourcePropType;
  date?: Moment;
}

const ClientHomeCalory: React.FC<props> = ({image, value, date}) => {
  const {meals} = useAppSelector(state => state.meals);
  const {t} = useTranslation();
  const navigation = useNavigation();

  const meal = useMemo(() => {
    return meals.find(
      item => item.mealTitle.toLowerCase() === value.toLowerCase(),
    );
  }, [meals, value]);
  const mealId = meal?._id;

  const consumedCalories =
    meal?.mealFoods?.reduce((acc, item) => acc + item.calories, 0) ?? 0;

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() =>
        navigation.navigate('MealHistory', {
          mealId: mealId,
          mealTitle: value,
          date,
        })
      }
      style={[styles.rowContainer, styles.caloryContainer]}>
      <View style={styles.rowContainer}>
        <Image source={image} style={styles.snackIcon} />
        <View style={{marginLeft: 15}}>
          <Text style={styles.headingText}>{t(value)}</Text>
          <Text style={styles.caloryText}>{consumedCalories} cal</Text>
        </View>
      </View>
      <TouchableOpacity
        style={styles.addButtonContainer}
        onPress={() =>
          navigation.navigate('MealHistory', {
            mealId: mealId,
            mealTitle: value,
            date,
          })
        }>
        <View
          style={{
            width: 26,
            height: 26,
            borderRadius: 8,
            backgroundColor: theme.lightColors?.black,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Image
            source={PNGIcons.PlusIcon}
            style={[styles.addButton, {tintColor: '#FFEDD7'}]}
          />
        </View>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default ClientHomeCalory;

const styles = StyleSheet.create({
  rowContainer: {flexDirection: 'row', alignItems: 'center'},
  caloryContainer: {
    borderColor: theme.lightColors?.grey1,
    borderWidth: 1,
    borderRadius: 14,
    paddingVertical: 11,
    paddingHorizontal: 12,
    justifyContent: 'space-between',
    marginTop: 14,
  },
  headingText: {
    marginBottom: 4,
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  caloryText: {
    fontFamily: Fonts.regular,
    color: '#2C2C2EB2',
    fontSize: 14,
  },
  addButtonContainer: {
    backgroundColor: '#00014',
    padding: 7,
    borderRadius: 8,
  },
  addButton: {height: 12, width: 12},
  snackIcon: {height: 40, width: 40},
});
