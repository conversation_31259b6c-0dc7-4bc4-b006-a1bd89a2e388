import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../utilities/theme';
import PopUpMenu from '../PopUpMenu/PopUpMenu';
import GeneralModal from '../Modals/GeneralModal/GeneralModal';
import PNGIcons from '../../assets/pngIcons';
import {IDocument} from '../../interfaces/IDocument';
import {useTranslation} from 'react-i18next';
interface Props {
  document: IDocument;
  client?: boolean;
  onDocumentPress: () => void;
  onDeletePress: () => void;
  onEditPress: () => void;
}

const DocumentComponent: React.FC<Props> = ({
  document,
  client,
  onDocumentPress,
  onDeletePress,
  onEditPress,
}) => {
  const {t} = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const handleDeleteOptionPress = () => {
    setShowModal(!showModal);
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onDocumentPress}>
      {/* <Image source={PNGIcons.PdfIcon} style={styles.fileImage} /> */}
      <View style={{flex: 1}}>
        <Text style={styles.heading}>{document.title}</Text>
        <Text numberOfLines={1} style={styles.description}>
          {document.description}
        </Text>
      </View>
      {client ? (
        <Image source={PNGIcons.ArrowRight} style={styles.arrowRight} />
      ) : (
        <PopUpMenu
          opened={isOpen}
          setOpened={setIsOpen}
          data={[
            {
              id: 1,
              label: t('Edit'),
              onPress: onEditPress,
            },
            {
              id: 2,
              label: t('Delete'),
              onPress: handleDeleteOptionPress,
              style: styles.deleteText,
            },
          ]}
        />
      )}
      <GeneralModal
        visible={showModal}
        onCancel={() => setShowModal(false)}
        primaryButtonOnpress={() => {
          handleDeleteOptionPress();
          onDeletePress();
        }}
        topRedTitle={t('Delete Document')}
        description={t('You are attempting to delete the document.')}
        primaryButtonName={t('Delete')}
        secondaryButtonText={t('Cancel')}
      />
    </TouchableOpacity>
  );
};

export default DocumentComponent;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 10,
    marginTop: 14,
  },
  heading: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    lineHeight: 19,
    color: theme.lightColors?.black,
  },
  description: {
    fontFamily: Fonts.regular,
    color: '#A0A0A0',
    marginTop: 8,
  },
  fileImage: {height: 32, width: 32, marginRight: 10},
  arrowRight: {width: 20, height: 20, marginLeft: 'auto'},
  deleteText: {color: theme.lightColors?.error},
});
