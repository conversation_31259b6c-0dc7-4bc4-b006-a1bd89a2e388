import {StyleSheet, Text, View} from 'react-native';
import React, {useRef} from 'react';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {Fonts} from '../../utilities/theme';
import {useTranslation} from 'react-i18next';
interface props {
  color: string;
}
const NutritionProgress: React.FC<props> = ({color}) => {
  const circularProgressRef = useRef(null);
  const {t} = useTranslation();

  return (
    <View style={styles.progressContainer}>
      <CircularProgressBase
        initialValue={1}
        ref={circularProgressRef}
        activeStrokeWidth={10}
        inActiveStrokeWidth={10}
        inActiveStrokeOpacity={0.2}
        value={20}
        radius={39}
        activeStrokeColor={color}
        inActiveStrokeColor={'#000000'}
        strokeLinecap={'round'}
        maxValue={28}>
        <Text style={styles.calLeftText}>14%</Text>
      </CircularProgressBase>
      <Text style={[styles.proteinText, {color: color}]}>{t('Proteins')}</Text>
    </View>
  );
};

export default NutritionProgress;

const styles = StyleSheet.create({
  calLeftText: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
  },
  proteinText: {
    fontSize: 14,
    fontFamily: Fonts.semiBold,
    marginTop: 8,
  },
  progressContainer: {alignItems: 'center', marginTop: 16},
});
