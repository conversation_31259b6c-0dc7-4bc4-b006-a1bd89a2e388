import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: theme.lightColors?.background,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 16,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
  dateText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}60`,
    marginTop: 3,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    marginRight: 28,
  },
  saveText: {
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
  cancelText: {color: '#2C2C2E99', fontFamily: Fonts.semiBold},
  activityIndicator: {
    height: 6,
    width: 6,
    marginLeft: 10,
  },
  modalListItem: {paddingHorizontal: 0, paddingVertical: 0},
  clientImage: {width: 40, height: 40, borderRadius: 40 / 2},
  listContent: {marginLeft: 0, paddingLeft: 0},
  rowButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  inputHeight: {
    height: 123,
  },
});
