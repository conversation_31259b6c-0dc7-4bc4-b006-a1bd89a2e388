import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';
import {GrayClock, GrayFire} from '../../assets/svgIcons';
import {useTranslation} from 'react-i18next';
interface props {
  onDeletePress?: () => void;
  onEditPress?: () => void;
  time?: string | number;
  workout: string;
  sets?: string;
  calories?: string | number;
}

const ExerciseComponent: React.FC<props> = ({
  onDeletePress,
  time,
  workout,
  sets,
  calories,
  onEditPress,
}) => {
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      <View>
        <Text style={styles.heading}>{workout}</Text>
        {time ? (
          <View style={styles.descriptionContainer}>
            <GrayClock />
            <Text style={styles.descText}>{`${time} ${t('mins')}`}</Text>
          </View>
        ) : null}
        {sets ? (
          <View style={styles.descriptionContainer}>
            <Text style={[styles.descText, {marginLeft: 0}]}>{sets}</Text>
          </View>
        ) : null}
        <View style={styles.descriptionContainer}>
          <Image
            resizeMode="contain"
            source={PNGIcons.RedIcon}
            style={styles.caloriesIcon}
          />
          <Text style={styles.descText}>{calories || '0'} cal</Text>
        </View>
      </View>
      <View style={{justifyContent: 'space-between'}}>
        <TouchableOpacity style={styles.editButton} onPress={onEditPress}>
          <Image source={PNGIcons.EditPencil} style={styles.editIcon} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.cancelButton} onPress={onDeletePress}>
          <Image source={PNGIcons.CrossIcon} style={styles.editIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ExerciseComponent;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    padding: 12,
    marginTop: 12,
  },
  editButton: {
    padding: 4,
    backgroundColor: theme.lightColors?.grey1,
    width: 26,
    borderRadius: 8,
    marginBottom: 8,
  },
  cancelButton: {
    padding: 4,
    backgroundColor: theme.lightColors?.grey1,
    width: 26,
    borderRadius: 8,
  },
  editIcon: {width: 18, height: 18},
  heading: {
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  descText: {
    color: '#5C5C5C',
    fontFamily: Fonts.regular,
    marginLeft: 5,
  },
  caloriesIcon: {tintColor: '#F64144', width: 10, height: 14},
});
