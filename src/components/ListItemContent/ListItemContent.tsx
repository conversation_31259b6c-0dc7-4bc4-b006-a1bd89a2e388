import {StyleSheet} from 'react-native';
import React from 'react';
import {Avatar, ListItem as ListItemRNE} from '@rneui/themed';
import {Fonts, theme} from '../../utilities/theme';
import PopUpMenu from '../PopUpMenu/PopUpMenu';
import PNGIcons from '../../assets/pngIcons';

interface Props {
  opened: boolean;
  setOpened: (opened: boolean) => void;
  data?: any;
  onClientPress?: () => void;
  name?: string;
  avatar?: string;
}

const ListItem: React.FC<Props> = ({
  opened,
  setOpened,
  data,
  onClientPress,
  name,
  avatar,
}) => {
  return (
    <ListItemRNE containerStyle={styles.container} onPress={onClientPress}>
      <Avatar
        rounded
        source={avatar ? {uri: avatar} : PNGIcons.User}
        size={34}
        avatarStyle={styles.avatarStyle}
      />
      <ListItemRNE.Content style={styles.contentStyle}>
        <ListItemRNE.Title style={styles.name}>{name}</ListItemRNE.Title>
      </ListItemRNE.Content>
      {/* <TouchableOpacity> */}
      <PopUpMenu opened={opened} setOpened={setOpened} data={data} />
      {/* </TouchableOpacity> */}
    </ListItemRNE>
  );
};

export default ListItem;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 16,
    backgroundColor: theme.lightColors?.white,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 12,
    marginBottom: 14,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
    marginLeft: 0,
    paddingLeft: 0,
  },
  avatarStyle: {height: 34, width: 34},
  contentStyle: {marginLeft: 0, paddingLeft: 0},
});
