import React, {
  useCallback,
  useMemo,
  useRef,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {StyleSheet} from 'react-native';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetModalProvider,
  BottomSheetBackdropProps,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import {useTranslation} from 'react-i18next';
import {theme} from '../../utilities/theme';

type CustomBottomSheetProps = {
  children: any;
  customSnapPoints?: string[];
};

export type CustomBottomSheetRef = {
  open: () => void;
  close: () => void;
};

const AttachBottomSheet = forwardRef<
  CustomBottomSheetRef,
  CustomBottomSheetProps
>(({children, customSnapPoints}, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(
    () => customSnapPoints || ['50%', '75%', '90%'],
    [],
  );

  const handlePresentModalPress = useCallback(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  const handleClosingSheetModal = useCallback(() => {
    bottomSheetModalRef.current?.close();
  }, []);

  useImperativeHandle(ref, () => ({
    open: handlePresentModalPress,
    close: handleClosingSheetModal,
  }));
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        style={[props.style]}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.2}
        pressBehavior="close"
      />
    ),
    [],
  );

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        backdropComponent={renderBackdrop}
        ref={bottomSheetModalRef}
        activeOffsetY={[-10, 10]} // Allows vertical movement before activating gesture
        failOffsetX={[-20, 20]} // Allows horizontal movement without closing
        enablePanDownToClose={false} // Disable swipe-to-close (use buttons instead)
        index={1}
        snapPoints={snapPoints}
        backgroundStyle={{backgroundColor: theme.lightColors?.white}}
        containerStyle={styles.mainContainer}>
        <BottomSheetView style={styles.contentContainer}>
          {children}
        </BottomSheetView>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
});

export default AttachBottomSheet;

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
  },
  mainContainer: {backgroundColor: 'rgba(0, 0, 0, 0.4)'},
});
