import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../utilities/theme';
import {useTranslation} from 'react-i18next';
import {CheckFill, CheckFillCross, PlayButton} from '../../assets/svgIcons';
interface Props {
  onPressItem: () => void;
  label: string;
  isActive: boolean;
  isMargin?: boolean;
  isSkip?: boolean;
}

const OnboardingCard: React.FC<Props> = ({
  onPressItem,
  label,
  isActive,
  isMargin,
  isSkip,
}) => {
  const {t} = useTranslation();

  return (
    <TouchableOpacity
      disabled={isActive}
      style={[styles.itemContainer, {marginBottom: isMargin ? 14 : 0}]}
      onPress={onPressItem}>
      <View style={{width: 26}} />
      <Text style={isSkip ? styles.skipText : styles.labelStyle}>
        {t(label)}
      </Text>
      {isSkip ? (
        <PlayButton width={26} height={26} />
      ) : isActive ? (
        <CheckFill width={26} height={26} />
      ) : (
        <CheckFillCross width={26} height={26} />
      )}
    </TouchableOpacity>
  );
};

export default OnboardingCard;

const styles = StyleSheet.create({
  itemContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 17,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 70,
    paddingHorizontal: 12,
  },
  skipText: {
    color: '#9EA0A5',
    textAlign: 'center',
    fontFamily: Fonts.medium,
    fontSize: 14,
  },
  labelStyle: {
    color: '#393C43',
    textAlign: 'center',
    fontFamily: Fonts.bold,
    fontSize: 18,
  },
});
