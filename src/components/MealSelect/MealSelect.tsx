import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../utilities/theme';
import {Image} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
interface props {
  name: string;
  calory?: string;
  tickIcon?: boolean;
  onPress: () => void;
  nameTick?: boolean;
}

const MealSelect: React.FC<props> = ({
  name,
  tickIcon,
  onPress,
  calory,
  nameTick,
}) => {
  return (
    <View style={styles.container}>
      <View>
        <View style={styles.nameContainer}>
          <Text style={styles.foodName}>{name}</Text>
          {nameTick && (
            <Image source={PNGIcons.TickFill} style={styles.tickFillIcon} />
          )}
        </View>
        {calory && <Text style={styles.calText}>{calory}</Text>}
      </View>
      <TouchableOpacity style={styles.plusButton} onPress={onPress}>
        <Image
          source={tickIcon ? PNGIcons.TickFillLarge : PNGIcons.PlusIcon}
          style={tickIcon ? styles.tickFillLarge : styles.plusIcon}
        />
      </TouchableOpacity>
    </View>
  );
};

export default MealSelect;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    borderWidth: 1,
    padding: 11,
    alignItems: 'center',
    marginTop: 12,
  },
  foodName: {
    fontFamily: Fonts.bold,
    marginRight: 5,
    color: theme.lightColors?.black,
  },
  tickFillIcon: {width: 14, height: 14},
  calText: {
    color: '#2C2C2E99',
    fontFamily: Fonts.regular,
    marginTop: 4,
  },
  plusButton: {
    backgroundColor: theme.lightColors?.grey6,
    borderRadius: 8,
    width: 26,
    height: 26,
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIcon: {width: 15, height: 15},
  tickFillLarge: {width: 22, height: 22},
  nameContainer: {flexDirection: 'row', alignItems: 'center'},
});
