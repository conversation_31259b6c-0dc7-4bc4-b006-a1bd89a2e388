import {t} from 'i18next';
import React, {useState} from 'react';
import {
  Pressable,
  LayoutAnimation,
  Text,
  Image,
  StyleSheet,
} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';

const CoachReviewCard = ({reviewText}: {reviewText: string}) => {
  const [expandedReview, setExpandedReview] = useState(false);

  return (
    <>
      <Pressable
        style={[styles.rowContainer, {marginTop: 16}]}
        onPress={() => {
          LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          setExpandedReview(!expandedReview);
        }}>
        <Text style={styles.headingText}>{t('Coach reviews')}</Text>
        <Image
          source={expandedReview ? PNGIcons.ArrowUpIcon : PNGIcons.ArrowDown}
          style={styles.arrowImage}
        />
      </Pressable>
      {expandedReview ? (
        <Text style={styles.reviewText}>{reviewText}</Text>
      ) : null}
    </>
  );
};

export default CoachReviewCard;

const styles = StyleSheet.create({
  arrowImage: {height: 18, width: 18},
  noFoodImage: {
    height: 120,
    width: 150,
  },
  reviewText: {
    color: theme.lightColors?.black,
    opacity: 0.7,
    fontSize: 12,
    lineHeight: 15,
    marginTop: 8,
    fontFamily: Fonts.regular,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  headingText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
});
