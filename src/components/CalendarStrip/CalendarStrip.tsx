import {View, Text, TouchableOpacity, Image} from 'react-native';
import React, {useState} from 'react';
import {styles} from './styles';
import PNGIcons from '../../assets/pngIcons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

import {Moment} from 'moment';

import moment from 'moment/min/moment-with-locales';

import {theme} from '../../utilities/theme';
import {useAppSelector} from '../../store';

interface Props {
  date: Moment;
  setDate: (d: Moment) => void;
  onPressLeftArrow?: () => void;
  onPressRightArrow?: () => void;
  onPressDate?: () => void;
}

export const CalendarStrip: React.FC<Props> = ({
  date,
  setDate,
  onPressLeftArrow,
  onPressRightArrow,
}) => {
  const [show, setShow] = useState(false);
  const language = useAppSelector(state => state.language.selectedLanguage);

  moment.locale(language);

  return (
    <View style={[styles.rowContainer, styles.calendarContainer]}>
      <TouchableOpacity onPress={onPressLeftArrow}>
        <Image source={PNGIcons.ArrowLeft} style={styles.arrowIcon} />
      </TouchableOpacity>
      <Text style={styles.dateText} onPress={() => setShow(true)}>
        {moment(date.format()).format('dddd')},{' '}
        {moment(date.format()).format('LL')}
      </Text>
      <TouchableOpacity
        onPress={onPressRightArrow}
        disabled={!moment(date).isBefore(moment().startOf('day'))}>
        <Image
          source={PNGIcons.ArrowRight}
          style={[
            styles.arrowIcon,
            !moment(date).isBefore(moment().startOf('day'))
              ? {tintColor: theme.lightColors?.grey2}
              : {},
          ]}
        />
      </TouchableOpacity>
      <DateTimePickerModal
        isVisible={show}
        date={new Date()}
        locale={language}
        maximumDate={new Date()}
        mode="date"
        onConfirm={date => {
          setShow(false);
          setDate(moment(date));
        }}
        onCancel={() => setShow(false)}
      />
    </View>
  );
};

export default CalendarStrip;
