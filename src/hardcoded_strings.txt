./src/constants/Chats.ts:16:    message: 'Hey there!',
./src/constants/Chats.ts:26:    message: 'How are you?',
./src/constants/Chats.ts:56:    message: 'Not much, just chilling.',
./src/constants/Chats.ts:66:    message: 'Anyone want to grab lunch?',
./src/constants/Chats.ts:6:    message: 'Hello world!',
./src/constants/Chats.ts:86:    message: 'Me too!',
./src/constants/Chats.ts:96:    message: 'Count me in as well.',
./src/hooks/models/useChats.ts:37:  //     console.log('New message recieved:', msg._id);
./src/navigation/RootNavigation.tsx:105:          console.log('AUTH_DENIED', authResult.message);
./src/screens/authflow/ClientQuestionare/Question3.tsx:77:                label=""
./src/screens/authflow/ClientQuestionare/Question3.tsx:91:                label=""
./src/screens/authflow/OTPVerification/OTPVerification.tsx:68:      if (errResp?.statusCode === 400 && errResp?.message === 'Invalid OTP') {
./src/screens/authflow/SignUp/SignUp.tsx:95:      if (errResp.data?.message == 'Email Already exist') {
./src/screens/clientflow/Activity/NewExercise.tsx:104:          placeholder="200"
./src/screens/clientflow/Activity/NewExercise.tsx:113:          placeholder="minutes"
./src/screens/clientflow/Activity/NewExercise.tsx:121:          label="Sets (optional)"
./src/screens/clientflow/Activity/NewExercise.tsx:129:          label="Reps (optional)"
./src/screens/clientflow/Activity/NewExercise.tsx:91:          placeholder="Bicycling"
./src/screens/clientflow/EditGoals/index.tsx:202:                  label=" "
./src/screens/clientflow/EditGoals/index.tsx:238:                  label=" "
./src/screens/clientflow/EditGoals/index.tsx:274:                  label=" "
./src/screens/clientflow/PersonalInformation/HeightEditor.tsx:103:                  label=""
./src/screens/clientflow/PersonalInformation/HeightEditor.tsx:104:                  placeholder="0"
./src/screens/clientflow/PersonalInformation/HeightEditor.tsx:117:                  label=""
./src/screens/clientflow/PersonalInformation/HeightEditor.tsx:118:                  placeholder="0"
./src/screens/homeflow/AddClient/index.tsx:16:  name: Yup.string().required('Name is required'),
./src/screens/homeflow/AddClient/index.tsx:17:  email: Yup.string().email('Invalid email').required('Email is required'),
./src/screens/homeflow/AddClient/index.tsx:48:        errorMessage = 'This client is already linked to a different coach';
./src/screens/homeflow/AddClient/index.tsx:53:        text1: 'Error',
./src/screens/homeflow/AddDocument/index.tsx:23:  name: yup.string().required('Name is required'),
./src/screens/homeflow/AddDocument/index.tsx:24:  description: yup.string().required('Description is required'),
./src/screens/homeflow/AddDocument/index.tsx:27:    .required('You need to select a document first'),
./src/screens/homeflow/ChatDetails/index.tsx:92:      console.log('Error fetching chat history', err);
./src/screens/homeflow/Chats/ChatItem.tsx:74:              title="1"
./src/screens/homeflow/ClientHistory/index.tsx:137:      .catch(error => console.log('ERR while fetching client details by ID'));
./src/screens/homeflow/ClientHistory/index.tsx:55:      console.log('Invalid timestamp:', timestamp);
./src/screens/homeflow/EditGoals/index.tsx:184:                  label=" "
./src/screens/homeflow/EditGoals/index.tsx:220:                  label=" "
./src/screens/homeflow/EditGoals/index.tsx:24:  calories: yup.string().required('Calories is required'),
./src/screens/homeflow/EditGoals/index.tsx:256:                  label=" "
./src/screens/homeflow/EditGoals/index.tsx:25:  proteins: yup.string().required('Proteins is required'),
./src/screens/homeflow/EditGoals/index.tsx:26:  proteinsPercentage: yup.string().required('Proteins % is required'),
./src/screens/homeflow/EditGoals/index.tsx:27:  carbs: yup.string().required('Carbs is required'),
./src/screens/homeflow/EditGoals/index.tsx:28:  carbsPercentage: yup.string().required('Carbs % is required'),
./src/screens/homeflow/EditGoals/index.tsx:29:  fat: yup.string().required('Fat is required'),
./src/screens/homeflow/EditGoals/index.tsx:30:  fatPercentage: yup.string().required('Fat % is required'),
./src/screens/homeflow/Home/MealInfo.tsx:69:            title="+ Add Reviews"
./src/screens/homeflow/WeightProgress/index.tsx:41:      console.log('Error selecting image', error);
./src/services/socketService.ts:32:      console.log('New message recieved:', msg);
./src/store/LanguagesSlice.ts:62:      console.error('Failed to load language from AsyncStorage:', error);
./src/store/LanguagesSlice.ts:79:      console.error('Failed to set language:', error);
