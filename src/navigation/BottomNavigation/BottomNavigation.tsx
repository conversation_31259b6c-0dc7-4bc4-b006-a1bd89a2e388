import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Image, Platform, Text} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';
import {Home, Clients, Chats, Profile} from '../../screens/homeflow';
import {IDocument} from '../../interfaces/IDocument';
import {IUser} from '../../interfaces/IUser.';
import {View} from 'react-native';
import {useAppSelector} from '../../store';
import {useTranslation} from 'react-i18next';

export type BottomStackParamList = {
  Home: undefined;
  Clients: undefined;
  Chats: undefined;
  Profile: undefined;
  Notifications: undefined;
  Archive: undefined;
  Language2: {isClient?: boolean};
  PreviousPassword: undefined;
  EmailVerification: undefined;
  ResetPassword: {
    previousPassword: string;
    reset_channel: 'forgotPassword' | 'changePassword';
  };
  OTPVerification: undefined;
  PasswordFeedback: undefined;
  TermsAndCondition: undefined;
  About: undefined;
  PersonalInfo: undefined;
  ViewSubscription: {isProfile: boolean};
  AddNewCard: {isProfile: boolean};
  Document: undefined;
  AddDocument: undefined;
  EditDocument: {document: IDocument};
  HelpCenter: undefined;
  ShareBarcode: undefined;
  ClientDetail: {details: IUser};
  ClientHistory: undefined;
  ChatDetails: {recipient?: IUser; chatId?: string};
  EditGoals: undefined;
  WeightProgress: undefined;
  AddClient: undefined;
  DocumentDetail: {document: IDocument};
  Subscription2: {isProfile: boolean};
};
const BottomStack = createBottomTabNavigator<BottomStackParamList>();

const BottomNavigation = () => {
  const unreadCounter = useAppSelector(state => state.chats.unreadCounter);
  const {t} = useTranslation();

  interface Props {
    icon: React.ReactElement;
  }

  const renderTabBarIcon: React.FC<Props> = ({icon}) => {
    return icon;
  };

  const renderTabBarIconWithDot = (
    iconSource: any,
    color: string,
    focused: boolean,
  ) => {
    return (
      <View style={{alignItems: 'center'}}>
        <Image
          source={iconSource}
          style={{
            width: 22,
            height: 22,
            tintColor: color,
          }}
        />
        {focused && (
          <View
            style={{
              width: 4,
              height: 4,
              borderRadius: 2,
              backgroundColor: theme.lightColors?.white,
              marginTop: 4,
            }}
          />
        )}
      </View>
    );
  };
  return (
    <BottomStack.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({color, size, focused}) => {
          let iconSource;
          if (route.name === 'Home') {
            iconSource = PNGIcons.HomeIcon;
          } else if (route.name === 'Clients') {
            iconSource = PNGIcons.SolarUser;
          } else if (route.name === 'Chats') {
            iconSource = PNGIcons.Message;
          } else if (route.name === 'Profile') {
            iconSource = PNGIcons.SolarUser2;
          }
          return renderTabBarIconWithDot(iconSource, color, focused);
        },
        tabBarActiveTintColor: theme.lightColors?.white,
        tabBarInactiveTintColor: `${theme.lightColors?.white}90`,
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.lightColors?.primary,
          height: Platform.OS === 'ios' ? 85 : 70,
          paddingBottom: 19,
        },
        tabBarLabelStyle: {
          fontSize: 10,
          fontFamily: Fonts.regular,
          marginTop: -10,
        },
        tabBarHideOnKeyboard: true,
      })}>
      <BottomStack.Screen
        name="Home"
        component={Home}
        options={{tabBarLabel: t('Home')}}
      />
      <BottomStack.Screen
        name="Clients"
        component={Clients}
        options={{tabBarLabel: t('Clients')}}
      />

      <BottomStack.Screen
        name="Chats"
        component={Chats}
        options={{
          tabBarIcon: ({focused, color}) => (
            <View style={{alignItems: 'center'}}>
              <View style={{position: 'relative'}}>
                {unreadCounter ? (
                  <View
                    style={{
                      position: 'absolute',
                      top: -5,
                      right: -7,
                      backgroundColor: 'red',
                      paddingHorizontal: 5,
                      paddingVertical: 2,
                      borderRadius: 100,
                      zIndex: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 8,
                        color: 'white',
                      }}>
                      {unreadCounter}
                    </Text>
                  </View>
                ) : null}

                <Image
                  resizeMode="contain"
                  source={PNGIcons.Message}
                  style={[
                    {width: 22, height: 22},
                    {
                      tintColor: color,
                    },
                  ]}
                />
              </View>
              {focused && (
                <View
                  style={{
                    width: 4,
                    height: 4,
                    borderRadius: 2,
                    backgroundColor: theme.lightColors?.white,
                    marginTop: 4,
                  }}
                />
              )}
            </View>
          ),

          tabBarLabel: t('Message'),
        }}
      />
      <BottomStack.Screen
        name="Profile"
        component={Profile}
        options={{tabBarLabel: t('Profile')}}
      />
    </BottomStack.Navigator>
  );
};

export default BottomNavigation;
