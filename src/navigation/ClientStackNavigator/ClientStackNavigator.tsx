import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {About, Notifications, TermsAndCondition} from '../../screens/homeflow';
import {BottomStackParamList} from '../BottomNavigation/BottomNavigation';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {BackArrow} from '../../assets/svgIcons';
import {Fonts, theme} from '../../utilities/theme';
import ClientBottomNavigation from '../ClientBottomNavigation/ClientBottomNavigation';
import MealHistory from '../../screens/clientflow/MealHistory';
import EditMeal from '../../screens/clientflow/EditMeal';
import ChatDetails from '../../screens/homeflow/ChatDetails';
import ClientPersonalInfo from '../../screens/clientflow/PersonalInformation';
import MyGoals from '../../screens/clientflow/MyGoals';
import EditGoals from '../../screens/clientflow/EditGoals';
import EditDocument from '../../screens/homeflow/EditDocument';
import AddDocument from '../../screens/homeflow/AddDocument';
import Document from '../../screens/homeflow/Document';
import DocumentDetail from '../../screens/homeflow/DocumentDetail';
import WeightProgress from '../../screens/homeflow/WeightProgress';
import CoachProfile from '../../screens/clientflow/CoachProfile';
import {
  Language,
  PasswordFeedback,
  PreviousPassword,
  ResetPassword,
  EmailVerification,
  OTPVerification,
} from '../../screens/authflow';
import HelpCenter from '../../screens/homeflow/HelpCenter';
import Activity from '../../screens/clientflow/Activity';
import AddExercise from '../../screens/clientflow/AddExercise';
import ScanCode from '../../screens/clientflow/ScanCode';
import FoodDetails from '../../screens/clientflow/FoodDetails/FoodDetails';
import {IFood} from '../../interfaces/IFoods';
import SearchFood from '../../screens/clientflow/SearchFood/SearchFood';
import {ICoachOfClient, IGoal} from '../../interfaces/IUser.';
import {useTranslation} from 'react-i18next';
import ViewSubscription from '../../screens/homeflow/ViewSubscription';
import {Moment} from 'moment';

export type ClientStackParamList = {
  BottomTabNavigator: BottomStackParamList;
  Notifications: undefined;
  MealHistory: {
    mealId?: string;
    mealTitle: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snacks';
    date: Moment;
  };
  SearchFood: {
    mealId?: string;
    mealTitle: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snacks';
    mealFoods?: IFood[] | undefined;
  };
  EditMeal: undefined;
  ChatDetails: {recipient?: ICoachOfClient; chatId?: string};
  PersonalInfo: undefined;
  MyGoals: undefined;
  EditGoals: {goal?: IGoal};
  Document: {isClient?: boolean};
  AddDocument: undefined;
  EditDocument: undefined;
  DocumentDetail: undefined;
  WeightProgress: {isClient?: boolean};
  CoachProfile: undefined;
  Language2: {isClient?: boolean};
  PreviousPassword: undefined;
  EmailVerification: undefined;
  OTPVerification: {email?: string};
  ResetPassword: undefined;
  PasswordFeedback: undefined;
  TermsAndCondition: undefined;
  Login: undefined;
  HelpCenter: undefined;
  ClientAbout: undefined;
  Activity: undefined;
  AddExercise: undefined;
  ScanCode: {mealId: string | undefined};
  FoodDetails: {food: IFood};
  ClientProfile: undefined;
  Profile: undefined;
  ViewSubscription: {isProfile: boolean};
};

const ClientStack = createNativeStackNavigator<ClientStackParamList>();

const ClientStackNavigator = () => {
  const {t} = useTranslation();

  return (
    <ClientStack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerLeft: () => (
          <TouchableOpacity
            style={{width: 30}}
            onPress={() => navigation.goBack()}
            hitSlop={styles.hitSlop}>
            <BackArrow stroke={theme.lightColors?.secondary} />
          </TouchableOpacity>
        ),
        headerTitleStyle: styles.headerTitleStyles,
      })}>
      <ClientStack.Screen
        name="BottomTabNavigator"
        component={ClientBottomNavigation}
      />
      <ClientStack.Screen
        name="Notifications"
        component={Notifications}
        options={{
          headerShown: true,
          headerTitle: 'Notifications',
        }}
      />

      <ClientStack.Screen
        name="MealHistory"
        component={MealHistory}
        options={route => ({
          headerShown: true,
          headerTitle: t(route.route.params.mealTitle),
        })}
        // options={route => {
        //   return {
        //     title: t(route?.route?.params?.isNew ? 'Add Card' : 'Checkout'),
        //   };
        // }}
      />

      <ClientStack.Screen
        name="SearchFood"
        component={SearchFood}
        options={{
          headerShown: true,
          headerTitle: t('Search Food'),
        }}
      />

      <ClientStack.Screen
        name="EditMeal"
        component={EditMeal}
        options={{
          headerShown: true,
          headerTitle: 'Wheat Atta Chapati',
        }}
      />
      <ClientStack.Screen
        name="ViewSubscription"
        component={ViewSubscription}
        options={{headerShown: true, headerTitle: t('Subscription')}}
      />
      <ClientStack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: false}}
      />
      <ClientStack.Screen
        name="PersonalInfo"
        component={ClientPersonalInfo}
        options={{headerShown: true, headerTitle: t('Personal Info')}}
      />

      <ClientStack.Screen
        name="MyGoals"
        component={MyGoals}
        options={{headerShown: true, headerTitle: t('My Goals')}}
      />

      <ClientStack.Screen
        name="EditGoals"
        component={EditGoals}
        options={{headerShown: true, headerTitle: t('Personal Info')}}
      />
      <ClientStack.Screen
        name="Document"
        component={Document}
        options={{headerShown: true, headerTitle: t('Document')}}
      />
      <ClientStack.Screen
        name="AddDocument"
        component={AddDocument}
        options={{headerShown: true, headerTitle: t('Add New Document')}}
      />

      <ClientStack.Screen
        name="EditDocument"
        component={EditDocument}
        options={{headerShown: true, headerTitle: t('Add New Document')}}
      />
      <ClientStack.Screen
        name="DocumentDetail"
        component={DocumentDetail}
        options={{headerShown: true, headerTitle: t('Details')}}
      />
      <ClientStack.Screen
        name="WeightProgress"
        component={WeightProgress}
        options={{headerShown: true, headerTitle: t('Weight Progress')}}
      />
      <ClientStack.Screen
        name="CoachProfile"
        component={CoachProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
      <ClientStack.Screen
        name="Language2"
        component={Language}
        options={{
          headerShown: true,
          headerTitle: t('Select Language'),
        }}
      />
      <ClientStack.Screen
        name="PreviousPassword"
        component={PreviousPassword}
        options={{
          headerShown: true,
          headerTitle: t('Change Password'),
        }}
      />
      <ClientStack.Screen
        name="EmailVerification"
        component={EmailVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <ClientStack.Screen
        name="OTPVerification"
        component={OTPVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <ClientStack.Screen
        name="ResetPassword"
        component={ResetPassword}
        options={{headerShown: true, headerTitle: t('Reset Password')}}
      />
      <ClientStack.Screen
        name="PasswordFeedback"
        component={PasswordFeedback}
      />
      <ClientStack.Screen
        name="TermsAndCondition"
        component={TermsAndCondition}
        options={{headerShown: true, headerTitle: t('Terms & Conditions')}}
      />
      <ClientStack.Screen
        name="HelpCenter"
        component={HelpCenter}
        options={{headerShown: true, headerTitle: t('Help Center')}}
      />
      <ClientStack.Screen
        name="ClientAbout"
        component={About}
        options={{headerShown: true, headerTitle: t('About')}}
      />

      <ClientStack.Screen
        name="Activity"
        component={Activity}
        options={{headerShown: true, headerTitle: t('Activity')}}
      />

      <ClientStack.Screen
        name="AddExercise"
        component={AddExercise}
        options={{headerShown: true, headerTitle: t('Add Exercise')}}
      />

      <ClientStack.Screen
        name="ScanCode"
        component={ScanCode}
        options={{headerShown: false, headerTitle: t('Scan Code')}}
      />

      <ClientStack.Screen
        name="FoodDetails"
        component={FoodDetails}
        options={({route}) => {
          return {headerShown: true, headerTitle: route.params.food.name};
        }}
      />
    </ClientStack.Navigator>
  );
};

export default ClientStackNavigator;
const styles = StyleSheet.create({
  hitSlop: {
    left: 15,
    right: 15,
    bottom: 15,
    top: 15,
  },
  headerTitleStyles: {
    fontSize: 16,
    color: theme.lightColors?.black,
    fontFamily: Fonts.semiBold,
  },
});
