import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {
  Language,
  Login,
  Onboard,
  SignUp,
  SelectRole,
  EmailVerification,
  ResetPassword,
  OTPVerification,
  PasswordFeedback,
  ProfileSetup,
  AddFirstClient,
  Subscription,
  AddNewCard,
  PaymentFeedback,
} from '../../screens/authflow';
import {BackArrow} from '../../assets/svgIcons';
import {Fonts, theme} from '../../utilities/theme';
import Question1 from '../../screens/authflow/ClientQuestionare/Question1';
import Question2 from '../../screens/authflow/ClientQuestionare/Question2';
import Question3 from '../../screens/authflow/ClientQuestionare/Question3';
import Question4 from '../../screens/authflow/ClientQuestionare/Question4';
import Question5 from '../../screens/authflow/ClientQuestionare/Question5';
import Question6 from '../../screens/authflow/ClientQuestionare/Question6';
import Question7 from '../../screens/authflow/ClientQuestionare/Question7';
import {useAppSelector} from '../../store';
import {useTranslation} from 'react-i18next';

export type AuthStackParamList = {
  Onboard: undefined;
  Language: undefined;
  Login: {isClient?: boolean};
  SignUp: {isClient?: boolean};
  SelectRole: undefined;
  EmailVerification: undefined;
  ResetPassword: {
    email: string;
    otp: string;
    reset_channel: 'forgotPassword' | 'changePassword';
  };
  OTPVerification: {email: string};
  PasswordFeedback: undefined;
  ProfileSetup: undefined;
  AddFirstClient: undefined;
  Subscription: undefined;
  AuthAddNewCard: undefined;
  PaymentFeedback: undefined;
  Question1: undefined;
  Question2: undefined;
  Question3: undefined;
  Question4: undefined;
  Question5: undefined;
  Question6: undefined;
  Question7: undefined;
};

const AuthStack = createNativeStackNavigator<AuthStackParamList>();

const AuthStackNavigator = () => {
  const {firstLaunch} = useAppSelector(state => state.user);
  const {t} = useTranslation();

  return (
    <AuthStack.Navigator
      initialRouteName="SelectRole"
      screenOptions={({navigation}) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerLeft: () => (
          <TouchableOpacity
            style={{width: 30}}
            onPress={() => navigation.goBack()}
            hitSlop={styles.hitSlop}>
            <BackArrow stroke={theme.lightColors?.secondary} />
          </TouchableOpacity>
        ),
        headerTitleStyle: styles.headerTitleStyles,
        navigationBarColor: theme.lightColors?.white,
      })}>
      {!firstLaunch && (
        <AuthStack.Screen name="Language" component={Language} />
      )}
      {!firstLaunch && (
        <AuthStack.Screen
          name="Onboard"
          component={Onboard}
          options={{
            headerShown: true,
            headerTitle: '',
          }}
        />
      )}
      <AuthStack.Screen
        name={'Login'}
        component={Login}
        options={{headerTitle: t('Login')}}
      />
      <AuthStack.Screen
        name={'SignUp'}
        component={SignUp}
        options={{headerTitle: t('Sign Up')}}
      />
      <AuthStack.Screen name="SelectRole" component={SelectRole} />
      <AuthStack.Screen
        name="EmailVerification"
        component={EmailVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <AuthStack.Screen
        name="ResetPassword"
        component={ResetPassword}
        options={{headerShown: true, headerTitle: t('Reset Password')}}
      />
      <AuthStack.Screen
        name="OTPVerification"
        component={OTPVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <AuthStack.Screen name="PasswordFeedback" component={PasswordFeedback} />
      <AuthStack.Screen name="ProfileSetup" component={ProfileSetup} />
      <AuthStack.Screen
        name="AddFirstClient"
        component={AddFirstClient}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen name="Subscription" component={Subscription} />
      <AuthStack.Screen
        name="AuthAddNewCard"
        component={AddNewCard}
        options={{headerShown: true, headerTitle: 'Payment'}}
      />
      <AuthStack.Screen
        name="Question1"
        component={Question1}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="Question2"
        component={Question2}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="Question3"
        component={Question3}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="Question4"
        component={Question4}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="Question5"
        component={Question5}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="Question6"
        component={Question6}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="Question7"
        component={Question7}
        options={{headerShown: false}}
      />
      <AuthStack.Screen name="PaymentFeedback" component={PaymentFeedback} />
    </AuthStack.Navigator>
  );
};

export default AuthStackNavigator;

const styles = StyleSheet.create({
  hitSlop: {
    left: 15,
    right: 15,
    bottom: 15,
    top: 15,
  },
  headerTitleStyles: {
    fontSize: 16,
    color: theme.lightColors?.black,
    fontFamily: Fonts.semiBold,
  },
});
