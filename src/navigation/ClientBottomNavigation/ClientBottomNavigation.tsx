import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Image, Platform, StyleSheet, View} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';
import {Chats} from '../../screens/homeflow';
import ClientHome from '../../screens/clientflow/ClientHome';
import ClientProfile from '../../screens/clientflow/ClientProfile';
import {IDocument} from '../../interfaces/IDocument';
import {ICoachOfClient} from '../../interfaces/IUser.';
import {useAppSelector} from '../../store';
import {Text} from 'react-native';
import {useTranslation} from 'react-i18next';

export type ClientBottomStackParamList = {
  Home: undefined;
  Profile: undefined;
  MealHistory: undefined;
  Chats: undefined;
  EditMeal: undefined;
  ChatDetails: {recipient?: ICoachOfClient; chatId?: string};
  PersonalInfo: undefined;
  MyGoals: undefined;
  EditGoals: undefined;
  Document: {isClient?: boolean};
  AddDocument: undefined;
  EditDocument: undefined;
  DocumentDetail: {document: IDocument};
  WeightProgress: {isClient?: boolean};
  CoachProfile: undefined;
  Language2: {isClient?: boolean};
  PreviousPassword: undefined;
  ResetPassword: undefined;
  PasswordFeedback: undefined;
  TermsAndCondition: undefined;
  HelpCenter: undefined;
  ClientAbout: undefined;
  Activity: undefined;
  AddExercise: undefined;
  ScanCode: undefined;
};
const ClientBottomStack =
  createBottomTabNavigator<ClientBottomStackParamList>();

const ClientBottomNavigation = () => {
  const unreadCounter = useAppSelector(state => state.chats.unreadCounter);
  const {t} = useTranslation();

  interface Props {
    icon: React.ReactElement;
  }

  const renderTabBarIcon: React.FC<Props> = ({icon}) => {
    return icon;
  };

  return (
    <ClientBottomStack.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({color, size}) => {
          let iconSource;
          if (route.name === 'Home') {
            iconSource = PNGIcons.HomeIcon;
          } else if (route.name === 'Chats') {
            iconSource = PNGIcons.Message;
          } else if (route.name === 'Profile') {
            iconSource = PNGIcons.SolarUser2;
          }
          return (
            <Image
              source={iconSource}
              style={[styles.iconStyle, {tintColor: color}]}
            />
          );
        },
        tabBarActiveTintColor: theme.lightColors?.white,
        tabBarInactiveTintColor: `${theme.lightColors?.white}90`,
        headerShown: false,
        tabBarStyle: styles.tabBarStyle,
        tabBarLabelStyle: styles.tabLabel,
        tabBarHideOnKeyboard: true,
      })}>
      <ClientBottomStack.Screen
        name="Home"
        component={ClientHome}
        options={{tabBarLabel: t('Home')}}
      />

      <ClientBottomStack.Screen
        name="Chats"
        component={Chats}
        options={{
          tabBarIcon: ({focused, color}) =>
            renderTabBarIcon({
              icon: (
                <View style={{position: 'relative'}}>
                  {unreadCounter ? (
                    <View style={styles.unreadContainer}>
                      <Text style={styles.unreadText}>{unreadCounter}</Text>
                    </View>
                  ) : null}

                  <Image
                    resizeMode="contain"
                    source={PNGIcons.Message}
                    style={[
                      styles.messageIcon,
                      {
                        tintColor: color,
                      },
                    ]}
                  />
                </View>
              ),
            }),

          tabBarLabel: t('Message'),
        }}
      />
      <ClientBottomStack.Screen
        name="Profile"
        component={ClientProfile}
        options={{tabBarLabel: t('Profile')}}
      />
    </ClientBottomStack.Navigator>
  );
};

export default ClientBottomNavigation;
const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: theme.lightColors?.primary,
    height: Platform.OS == 'ios' ? 65 : 55,
    paddingBottom: 19,
  },
  iconStyle: {width: 24, height: 24},
  tabLabel: {
    fontSize: 10,
    fontFamily: Fonts.regular,
    marginTop: -10,
  },
  unreadContainer: {
    position: 'absolute',
    top: -5,
    right: -7,
    backgroundColor: 'red',
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderRadius: 100,
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unreadText: {
    fontSize: 8,
    color: 'white',
  },
  messageIcon: {width: 22, height: 22},
});
