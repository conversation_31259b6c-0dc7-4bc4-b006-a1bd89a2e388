Searching for t() function calls in: ./
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 15
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 33
Text:         setTimeout(() => {
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 36
Text:           setTimeout(() => {
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 74
Text:           {t('Add your client through email')}
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 77
Text:           label={t('Name')}
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 82
Text:             formik.touched.name ? t(formik.errors.name as string) : undefined
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 86
Text:           label={t('Email')}
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 91
Text:             formik.touched.email ? t(formik.errors.email as string) : undefined
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 96
Text:         title={t('Send Invite')}
----------------------------------------
File: ./screens/homeflow/AddClient/index.tsx
Line: 105
Text:         paragraph={t(
----------------------------------------
File: ./screens/homeflow/TermsAndCondition/TermsAndCondition.tsx
Line: 28
Text:             {t(
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 25
Text:           <Text style={styles.packageTypeText}>{t('Basic')}</Text>
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 27
Text:             {t('$900/')}
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 28
Text:             <Text style={styles.largeTitle2}>{t('Month')}</Text>
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 30
Text:           <Text style={styles.planIncludeText}>{t('Plan include')}</Text>
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 48
Text:           <Text style={styles.valditText}>{t('Valid for 1 month')}</Text>
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 50
Text:             title={t('Update plan')}
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 61
Text:             <Text style={styles.cancelText}>{t('Cancel')}</Text>
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 64
Text:             title={t('Buy New')}
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 74
Text:         topRedTitle={t('Cancel Subscription')}
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 75
Text:         description={t('You are attempting to cancel your subscription.')}
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 76
Text:         primaryButtonName={t('Yes, Cancel')}
----------------------------------------
File: ./screens/homeflow/ViewSubscription/index.tsx
Line: 78
Text:         secondaryButtonText={t('No')}
----------------------------------------
File: ./screens/homeflow/DocumentDetail/index.tsx
Line: 28
Text:         <Text style={styles.heading}>{t('Name')}</Text>
----------------------------------------
File: ./screens/homeflow/DocumentDetail/index.tsx
Line: 31
Text:           {t('Description')}
----------------------------------------
File: ./screens/homeflow/DocumentDetail/index.tsx
Line: 44
Text:             title={t('Delete')}
----------------------------------------
File: ./screens/homeflow/DocumentDetail/index.tsx
Line: 51
Text:           <Button title={t('Edit')} onPress={() => handleEditDocument()} />
----------------------------------------
File: ./screens/homeflow/Chats/Chats.tsx
Line: 49
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/Chats/Chats.tsx
Line: 87
Text:             <Text style={styles.myCoachText}>{t('My Coach')}</Text>
----------------------------------------
File: ./screens/homeflow/Chats/Chats.tsx
Line: 89
Text:               {t('Contact Your Coach Anytime!')}
----------------------------------------
File: ./screens/homeflow/Chats/Chats.tsx
Line: 131
Text:             <Text style={styles.noChatHeading}>{t('No new messages')}</Text>
----------------------------------------
File: ./screens/homeflow/Chats/Chats.tsx
Line: 132
Text:             <Text style={styles.notChatText}>{t('Your inbox is empty')}</Text>
----------------------------------------
File: ./screens/homeflow/Chats/ChatItem.tsx
Line: 49
Text:             {moment(chatItem.lastMessage.createdAt).format('LT')}
----------------------------------------
File: ./screens/homeflow/About/About.tsx
Line: 31
Text:             {t(
----------------------------------------
File: ./screens/homeflow/About/About.tsx
Line: 36
Text:             {t('Why Choose Food Log?')}
----------------------------------------
File: ./screens/homeflow/About/About.tsx
Line: 41
Text:               {t(
----------------------------------------
File: ./screens/homeflow/About/About.tsx
Line: 49
Text:               {t(
----------------------------------------
File: ./screens/homeflow/About/About.tsx
Line: 57
Text:               {t(
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 46
Text:               {t('Total clients:')}{' '}
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 65
Text:                           label: t('Unarchive'),
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 67
Text:                             handleArchiveClient(item._id, false);
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 72
Text:                           label: t('Delete'),
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 86
Text:               <Text style={styles.emptyText}>{t('No archived client')}</Text>
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 95
Text:           handleDeleteClient(clientID);
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 99
Text:         topRedTitle={t('Delete Client')}
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 100
Text:         description={t('You are attempting to delete client.')}
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 101
Text:         primaryButtonName={t('Yes, Delete')}
----------------------------------------
File: ./screens/homeflow/Archive/Archive.tsx
Line: 102
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 22
Text: const validationSchema = yup.object().shape({
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 52
Text:       const resp = await DocumentServices.CreateDocument(user._id, payload);
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 56
Text:         dispatch(addNewDocument(resp.data));
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 60
Text:           text1: t('Success'),
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 61
Text:           text2: t('New document added successfully'),
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 67
Text:         text1: t('Error'),
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 68
Text:         text2: t('Error while creating a document'),
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 87
Text:       const resp = await DocumentServices.UploadDocument(data);
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 95
Text:         text1: t('Error'),
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 96
Text:         text2: t("Couldn't upload the file."),
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 115
Text:           placeholder={t('Name')}
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 116
Text:           label={t('Name')}
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 122
Text:             formik.touched.name ? t(formik.errors.name as string) : undefined
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 127
Text:           placeholder={t('Description')}
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 128
Text:           label={t('Description')}
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 134
Text:               ? t(formik.errors.description as string)
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 157
Text:               <Text style={styles.uploadText}>{t('Upload document')}</Text>
----------------------------------------
File: ./screens/homeflow/AddDocument/index.tsx
Line: 169
Text:         title={t('Save')}
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 56
Text:             setClickedWeight(undefined);
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 60
Text:             setClickedWeight(weight);
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 71
Text:             <Text style={styles.recordText}>{t('Progress Record')}</Text>
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 80
Text:                     date={moment(+item.createdAt).format('DD/MM/YY')}
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 104
Text:             <Text style={styles.modalHeading}>{t('Add weight')}</Text>
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 106
Text:             <FormInput label={t('Enter you weight')} />
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 118
Text:                 onPress={() => setUnit('kg')}>
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 136
Text:                 onPress={() => setUnit('lb')}>
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 145
Text:             <Text style={styles.uploadText}>{t('Upload image')}</Text>
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 165
Text:               <Text style={styles.deleteFood}>{t('Cancel')}</Text>
----------------------------------------
File: ./screens/homeflow/WeightProgress/index.tsx
Line: 168
Text:               title={t('Add')}
----------------------------------------
File: ./screens/homeflow/Document/index.tsx
Line: 38
Text:   async function deleteDocument(documentId: string) {
----------------------------------------
File: ./screens/homeflow/Document/index.tsx
Line: 40
Text:       dispatch(removeDocument({_id: documentId}));
----------------------------------------
File: ./screens/homeflow/Document/index.tsx
Line: 41
Text:       await DocumentServices.DeleteDocument(documentId);
----------------------------------------
File: ./screens/homeflow/Document/index.tsx
Line: 63
Text:               onDeletePress={() => deleteDocument(item._id)}
----------------------------------------
File: ./screens/homeflow/Document/index.tsx
Line: 73
Text:         <Text style={styles.emptyText}>{t('No Documents Found')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientDetail/DetailItem.tsx
Line: 28
Text:       <Text style={[styles.smallHeading, headingStyle]}>{t(title)}</Text>
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 62
Text:   useLayoutEffect(() => {
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 82
Text:                 label: t('Archive'),
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 87
Text:                 label: t('Delete'),
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 104
Text:         setChatsList(resp.data?.client?.chats);
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 118
Text:   useFocusEffect(
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 149
Text:           <Text style={styles.title}>{t('Daily Goals')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 175
Text:               {netCaloriesLeft(userInfo.goal, userInfo.usedData) || 0}
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 177
Text:             <Text style={styles.calLeftText}>{t('calories')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 220
Text:         topRedTitle={t('Delete Client')}
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 221
Text:         description={t('You are attempting to delete client.')}
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 222
Text:         primaryButtonName={t('Yes, Delete')}
----------------------------------------
File: ./screens/homeflow/ClientDetail/index.tsx
Line: 223
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/homeflow/Clients/Clients.tsx
Line: 36
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/Clients/Clients.tsx
Line: 65
Text:                   {t('Total Clients')}:{' '}
----------------------------------------
File: ./screens/homeflow/Clients/Clients.tsx
Line: 108
Text:                     {t('No clients found')}
----------------------------------------
File: ./screens/homeflow/Clients/Clients.tsx
Line: 111
Text:                     {t(
----------------------------------------
File: ./screens/homeflow/Clients/PopUpMenu.tsx
Line: 34
Text:             title={t('Sort by')}
----------------------------------------
File: ./screens/homeflow/Clients/PopUpMenu.tsx
Line: 54
Text:                   <Text style={[styles.menuLabel]}>{t(item.label)}</Text>
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 29
Text:     handleDeleteClient(clientID);
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 45
Text:             label: t('Message'),
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 50
Text:             label: t('Archive'),
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 52
Text:               handleArchiveClient(client._id, true);
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 57
Text:             label: t('Delete'),
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 70
Text:         topRedTitle={t('Delete Client')}
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 71
Text:         description={t('You are attempting to delete client.')}
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 72
Text:         primaryButtonName={t('Yes, Delete')}
----------------------------------------
File: ./screens/homeflow/Clients/ClientListItem.tsx
Line: 73
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 23
Text: const validationSchema = yup.object().shape({
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 66
Text:             text1: t('Success'),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 67
Text:             text2: t('Goal updated successfully.'),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 76
Text:           text1: t('ERROR'),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 77
Text:           text2: t("Couldn't update the goal."),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 95
Text:             text1: t('Success'),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 96
Text:             text2: t('Goal created successfully.'),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 105
Text:           text1: t('ERROR'),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 106
Text:           text2: t("Couldn't update the review."),
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 130
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 145
Text:               {t('Set your client’s daily goals.')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 150
Text:               label={t('Calories Goal')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 151
Text:               unitText={t('cal.')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 157
Text:                   ? t(formik.errors.calories as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 167
Text:                   label={t('Proteins Goal')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 175
Text:                       ? t(formik.errors.proteins as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 191
Text:                       ? t(formik.errors.proteinsPercentage as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 203
Text:                   label={t('Carbs Goal')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 211
Text:                       ? t(formik.errors.carbs as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 227
Text:                       ? t(formik.errors.carbsPercentage as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 239
Text:                   label={t('Fat Goal')}
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 247
Text:                       ? t(formik.errors.fat as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 263
Text:                       ? t(formik.errors.fatPercentage as string)
----------------------------------------
File: ./screens/homeflow/EditGoals/index.tsx
Line: 274
Text:             title={t('Save Changes')}
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 64
Text:   // const socket = getSocket();
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 79
Text:             createdAt: moment(chatItem.createdAt),
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 98
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 143
Text:           dispatch(updateChat({chat: {...item, unReadMsgCount: 0}}));
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 169
Text:     setText(text + emoji);
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 173
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 199
Text:             placeholder={t('Your message')}
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 297
Text:             {t('Upload')}
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 304
Text:               <Text style={styles.text}>{t('Camera')}</Text>
----------------------------------------
File: ./screens/homeflow/ChatDetails/index.tsx
Line: 313
Text:               <Text style={styles.text}>{t('Gallery')}</Text>
----------------------------------------
File: ./screens/homeflow/ShareBarcode/index.tsx
Line: 37
Text:       text1: t('Copied'),
----------------------------------------
File: ./screens/homeflow/ShareBarcode/index.tsx
Line: 38
Text:       text2: t('Referral code copied to clipboard'),
----------------------------------------
File: ./screens/homeflow/ShareBarcode/index.tsx
Line: 49
Text:         <Text style={[styles.QRText, styles.QRMargins]}>{t('QR')}</Text>
----------------------------------------
File: ./screens/homeflow/ShareBarcode/index.tsx
Line: 58
Text:         <Text style={[styles.QRText, {marginTop: 82}]}>{t('Code')}</Text>
----------------------------------------
File: ./screens/homeflow/ShareBarcode/index.tsx
Line: 70
Text:         title={t('Share')}
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 32
Text:   const [selectedDate, setSelectedDate] = useState(moment());
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 46
Text:     if (!timestamp) return t('No date available');
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 50
Text:       typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 52
Text:     const momentDate = moment(numericTimestamp);
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 56
Text:       return t('Invalid Date');
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 59
Text:     const now = moment();
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 61
Text:       ? t('Today')
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 62
Text:       : momentDate.isSame(now.clone().subtract(1, 'day'), 'day')
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 63
Text:       ? t('Yesterday')
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 64
Text:       : momentDate.format('MMM Do, YYYY');
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 66
Text:     return `${dayLabel} - ${momentDate.format('h:mm a')}`;
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 71
Text:     const currentDate = selectedDate.format('YYYY-MM-DD');
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 73
Text:       const resp = await MealServices.GetMealsByClient(clientId, currentDate);
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 116
Text:       const resp = await WeightServices.GetWeightByClient(clientId);
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 149
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 151
Text:     if (selectedDate) getActivity(new Date(selectedDate.format()));
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 154
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 172
Text:           <Text style={styles.monthName}>{selectedDate.format('MMMM')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 227
Text:                 <Text style={styles.summaryLabel}>{t('Daily Target')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 228
Text:                 <Text style={styles.summaryValue}>{`${clientGoal?.calories} ${t(
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 233
Text:                 <Text style={styles.summaryLabel}>{t('Consumed')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 236
Text:                 } ${t('cal')}`}</Text>
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 240
Text:                 <Text style={styles.summaryLabel}>{t('Burned')}</Text>
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 243
Text:                 } ${t('cal')}`}</Text>
----------------------------------------
File: ./screens/homeflow/ClientHistory/index.tsx
Line: 299
Text:           <Text style={styles.emptyText}>{t('No Meal in this day')}.</Text>
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 24
Text: const editDocSchema = yup.object({
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 37
Text:         updateDocument({
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 45
Text:       await DocumentServices.UpdateDocument(document._id, formData);
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 53
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 54
Text:           text2: t('Make sure your internet is working.'),
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 59
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 60
Text:           text2: t("Couldn't update the document."),
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 80
Text:           label={t('Name')}
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 84
Text:             formik.touched.title ? t(formik.errors.title as string) : undefined
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 89
Text:           label={t('Description')}
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 95
Text:               ? t(formik.errors.description as string)
----------------------------------------
File: ./screens/homeflow/EditDocument/index.tsx
Line: 103
Text:         title={t('Save')}
----------------------------------------
File: ./screens/homeflow/Home/MealInfo.tsx
Line: 25
Text:       <Text style={styles.detailsText}>{t('Details')}</Text>
----------------------------------------
File: ./screens/homeflow/Home/MealInfo.tsx
Line: 50
Text:           nutritionLabel={t('Protein')}
----------------------------------------
File: ./screens/homeflow/Home/MealInfo.tsx
Line: 55
Text:           nutritionLabel={t('Carbs')}
----------------------------------------
File: ./screens/homeflow/Home/MealInfo.tsx
Line: 61
Text:           nutritionLabel={t('Fat')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 193
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 213
Text:             {moment(+mealPost.createdAt).isSame(moment(), 'day')
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 214
Text:               ? `${t('Today')} - ${moment(+mealPost.createdAt).format(
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 217
Text:               : moment(+mealPost.createdAt).format('MMM DD - hh:mm a')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 241
Text:         <Text style={styles.breakfastText}>{t(mealPost.mealTitle)}</Text>
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 247
Text:           <Text style={styles.detailsText}>{t('Details')}</Text>
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 270
Text:           <Text style={{fontSize: 14}}>{t('cal')}</Text>
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 276
Text:           nutritionLabel={t('Protein')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 281
Text:           nutritionLabel={t('Carbs')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 286
Text:           nutritionLabel={t('Fat')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 301
Text:             title={!mealPost.review?.text ? t('+ Add Reviews') : t('Reviewed')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 317
Text:           <Text style={styles.name}>{t('Reviews')}</Text>
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 349
Text:         topRedTitle={t('Confirmation')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 350
Text:         description={t('You are trying to delete the review.')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 352
Text:         primaryButtonName={t('Delete')}
----------------------------------------
File: ./screens/homeflow/Home/MealPost.tsx
Line: 354
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/homeflow/Home/Home.tsx
Line: 28
Text:   useEffect(() => {
----------------------------------------
File: ./screens/homeflow/Home/Home.tsx
Line: 73
Text:               {t('No meal added by client.')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 123
Text:             label={t('Personal Information')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 124
Text:             title={t('General')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 130
Text:             label={t('Subscription')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 137
Text:             label={t('Document')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 142
Text:             label={t('Change Password')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 147
Text:             label={t('Language')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 152
Text:             label={t('Terms & Conditions')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 153
Text:             title={t('Help & Support')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 158
Text:             label={t('Help Center')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 169
Text:             label={t('About')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 180
Text:             label={t('Log Out')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 181
Text:             title={t('Log Out')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 190
Text:         topRedTitle={t('Log Out')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 191
Text:         description={t('You are attempting to log out.')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 192
Text:         primaryButtonName={t('Yes, Log Out')}
----------------------------------------
File: ./screens/homeflow/Profile/Profile.tsx
Line: 193
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 27
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 121
Text:       dob: moment(userData.dob).format('YYYY-MM-DD'),
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 150
Text:           label={t('Name')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 157
Text:             formik.touched.name ? t(formik.errors.name as string) : undefined
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 161
Text:           label={t('Email')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 165
Text:             formik.touched.email ? t(formik.errors.email as string) : undefined
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 170
Text:           label={t('Age')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 175
Text:             formik.touched.dob ? t(formik.errors.dob as string) : undefined
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 180
Text:         <Text style={styles.dobText}>{t('Date of birth')}</Text>
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 187
Text:               ? t('Pick Date')
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 188
Text:               : moment(formik.values?.dob).format('MMMM Do, YYYY')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 193
Text:           label={t('Experience (years)')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 199
Text:               ? t(formik.errors.experience as string)
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 205
Text:           label={t('About')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 210
Text:             formik.touched.about ? t(formik.errors.about as string) : undefined
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 217
Text:           title={t('Save Changes')}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 225
Text:         date={moment(formik.values.dob).toDate()}
----------------------------------------
File: ./screens/homeflow/PersonalInformation/index.tsx
Line: 229
Text:           formik.setFieldValue('dob', moment(date).format('YYYY-MM-DD'));
----------------------------------------
File: ./screens/homeflow/Notifications/NotificationItem.tsx
Line: 19
Text:           <Text style={styles.timeText}>{t('Nov 21')}</Text>
----------------------------------------
File: ./screens/homeflow/Notifications/NotificationItem.tsx
Line: 55
Text:               {t('New message!')}
----------------------------------------
File: ./screens/homeflow/Notifications/NotificationItem.tsx
Line: 58
Text:               {t("You've a new message.")}
----------------------------------------
File: ./screens/homeflow/HelpCenter/index.tsx
Line: 19
Text:         <FormInput label={t('Name')} labelStyle={{marginTop: 41}} />
----------------------------------------
File: ./screens/homeflow/HelpCenter/index.tsx
Line: 20
Text:         <FormInput label={t('Subject')} />
----------------------------------------
File: ./screens/homeflow/HelpCenter/index.tsx
Line: 22
Text:           label={t('Your message')}
----------------------------------------
File: ./screens/homeflow/HelpCenter/index.tsx
Line: 29
Text:         title={t('Send')}
----------------------------------------
File: ./screens/clientflow/AddExercise/index.tsx
Line: 51
Text:           onChangeText={v => setSearchText(v)}
----------------------------------------
File: ./screens/clientflow/AddExercise/index.tsx
Line: 67
Text:               onPress={() => handleCategorySelect(item)}>
----------------------------------------
File: ./screens/clientflow/ScanCode/index.tsx
Line: 37
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/ScanCode/index.tsx
Line: 111
Text:         fetchClient();
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 57
Text:   React.useEffect(() => {
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 61
Text:   async function handleSubmit() {
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 77
Text:       fetchClient();
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 79
Text:       setTimeout(() => {
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 107
Text:           onChangeText={v => setSearchText(v)}
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 119
Text:               {t('Results found for')}{' '}
----------------------------------------
File: ./screens/clientflow/SearchFood/SearchFood.tsx
Line: 176
Text:             {t('Add to')} {route.params.mealTitle}
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 34
Text:   const validationSchema = yup.object({
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 35
Text:     title: yup.string().required(t('Enter title')),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 36
Text:     calories: yup.string().required(t('Enter calories')),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 37
Text:     duration: yup.string().required(t('Enter duration')),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 59
Text:           calories: parseInt(calories),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 62
Text:           duration: parseInt(duration),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 63
Text:           sets: parseInt(sets),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 64
Text:           reps: parseInt(reps),
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 84
Text:         <Text style={styles.modalHeading}>{t('New Exercise')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 88
Text:           label={t('Title')}
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 96
Text:           label={t('Category')}
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 103
Text:           label={t('Calories')}
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 112
Text:           label={t('Duration (mins)')}
----------------------------------------
File: ./screens/clientflow/Activity/NewExercise.tsx
Line: 137
Text:           title={t('Add')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 107
Text:             <Text style={styles.watchHeading}>{t('Connect Wearables')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 109
Text:               {t('Auto calculate your steps and activity')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 117
Text:               <Text style={styles.connectText}>{t('Connect Now')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 122
Text:           <Text style={styles.semiBoldText}>{t('Today’s Exercise')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 125
Text:             <Text style={{fontFamily: Fonts.regular}}>{t('cal')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 137
Text:             {t('No Activity found for today')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 141
Text:           reviewText={t(
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 147
Text:         title={t('+ Add Exercise')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 179
Text:             <Text style={styles.modalHeading}>{t('Push ups')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 183
Text:               label={t('No. of sets')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 189
Text:               label={t('No. of reps')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 194
Text:               <Text style={styles.inputLabel}>{t('Calories burned')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 195
Text:               <Text>0 {t('cal')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 200
Text:             title={t('Add')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 218
Text:             <Text style={styles.modalHeading}>{t('Aerobics')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 222
Text:               label={t('Minutes')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 227
Text:               <Text style={styles.inputLabel}>{t('Calories burned')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 228
Text:               <Text>0 {t('cal')}</Text>
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 232
Text:             title={t('Add')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 247
Text:         topRedTitle={t('Delete Exercise')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 248
Text:         description={t('You are attempting to delete exercise.')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 249
Text:         primaryButtonName={t('Delete')}
----------------------------------------
File: ./screens/clientflow/Activity/index.tsx
Line: 250
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/clientflow/FoodDetails/FoodDetails.tsx
Line: 56
Text:       <Text style={styles.textHeading}>{t('Nutritional Information')}</Text>
----------------------------------------
File: ./screens/clientflow/FoodDetails/FoodDetails.tsx
Line: 59
Text:           {food?.calories} {t('cal')}
----------------------------------------
File: ./screens/clientflow/FoodDetails/FoodDetails.tsx
Line: 82
Text:               {t('Protiens')}
----------------------------------------
File: ./screens/clientflow/FoodDetails/FoodDetails.tsx
Line: 105
Text:               {t('Carbs')}
----------------------------------------
File: ./screens/clientflow/FoodDetails/FoodDetails.tsx
Line: 128
Text:               {t('Fats')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 48
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 87
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 129
Text:             await fetchClient();
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 133
Text:               text1: t('Success'),
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 134
Text:               text2: t('Food updated successfully.'),
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 143
Text:           showErrorToast(t('Make sure your internet is working.'));
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 145
Text:           showErrorToast(t('Something went wrong.'));
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 179
Text:           await fetchClient();
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 185
Text:             text1: t('Success'),
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 186
Text:             text2: t('Food added successfully.'),
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 194
Text:           showErrorToast(t('Make sure your internet is working.'));
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 196
Text:           showErrorToast(t('Something went wrong.'));
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 255
Text:         await fetchClient();
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 261
Text:           text1: t('Success'),
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 262
Text:           text2: t('Food deleted from the meal.'),
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 273
Text:         showErrorToast(t('Make sure your internet is working.'));
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 275
Text:         showErrorToast(t('Somehting went wrong.'));
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 307
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 321
Text:         {!moment(date).isBefore(moment().startOf('day')) ? (
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 338
Text:           {!moment(date).isBefore(moment().startOf('day')) ? (
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 369
Text:               {!moment(date).isBefore(moment().startOf('day'))
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 370
Text:                 ? t(`Today's ${route.params.mealTitle}`)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 371
Text:                 : t(
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 372
Text:                     `${moment(date).format('DD MMM')} ${
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 402
Text:                 <Text style={styles.coachReviewText}>{t('Coach Reviews')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 437
Text:                   <Text style={styles.noFoodHeading}>{t('No Food added')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 439
Text:                     {t('Please add foods to the meal to see here')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 442
Text:                 {!moment(date).isBefore(moment().startOf('day')) ? (
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 444
Text:                     title={t('Add Food')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 463
Text:             reviewText={t(
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 483
Text:           <Text style={styles.modalHeading}>{t('Add New Food')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 490
Text:               label={t('Name of Food')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 495
Text:                   ? t(formik.errors.name as string)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 503
Text:               label={t('Quantity')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 508
Text:                   ? t(formik.errors.quantity as string)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 516
Text:               <Text style={styles.inputLabel}>{t('Calories')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 518
Text:                 rightIcon={<Text style={styles.unitText}>{t('cal')}</Text>}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 528
Text:                     ? t(formik.errors.calories as string)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 537
Text:               <Text style={styles.inputLabel}>{t('Proteins')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 549
Text:                     ? t(formik.errors.proteins as string)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 555
Text:               <Text style={styles.inputLabel}>{t('Carbs')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 567
Text:                     ? t(formik.errors.carbs as string)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 573
Text:               <Text style={styles.inputLabel}>{t('Fats')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 585
Text:                     ? t(formik.errors.fats as string)
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 600
Text:               <Text style={styles.deleteFood}>{t('Cancel')}</Text>
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 604
Text:               title={t('Add')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 620
Text:         topRedTitle={t('Delete Food')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 621
Text:         description={t('You are attempting to delete food.')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 622
Text:         primaryButtonName={t('Delete')}
----------------------------------------
File: ./screens/clientflow/MealHistory/index.tsx
Line: 623
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 28
Text: const validationSchema = yup.object().shape({
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 89
Text:             text1: t('Success'),
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 90
Text:             text2: t('Goal updated successfully'),
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 99
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 100
Text:           text2: t("Couldn't update the goal"),
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 119
Text:             text1: t('Success'),
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 120
Text:             text2: t('Goal created successfully.'),
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 150
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 164
Text:             <Text style={styles.heading}>{t('Set your daily goals')}.</Text>
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 168
Text:               label={t('Calories Goal')}
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 175
Text:                   ? t(formik.errors.calories as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 185
Text:                   label={t('Proteins Goal')}
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 193
Text:                       ? t(formik.errors.proteins as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 209
Text:                       ? t(formik.errors.proteinsPercentage as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 221
Text:                   label={t('Carbs Goal')}
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 229
Text:                       ? t(formik.errors.carbs as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 245
Text:                       ? t(formik.errors.carbsPercentage as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 257
Text:                   label={t('Fats Goal')}
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 265
Text:                       ? t(formik.errors.fat as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 281
Text:                       ? t(formik.errors.fatPercentage as string)
----------------------------------------
File: ./screens/clientflow/EditGoals/index.tsx
Line: 291
Text:             title={t('Save Changes')}
----------------------------------------
File: ./screens/clientflow/ClientHome/addWeight.schema.ts
Line: 3
Text: export const addWeightSchema = yup.object().shape({
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 97
Text:         const resp = await WeightServices.UploadImagesForWeight(formData);
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 109
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 110
Text:           text2: t("Couldn't upload the images."),
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 128
Text:       const resp = await WeightServices.AddWeight({
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 139
Text:           text1: t('Success'),
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 140
Text:           text2: t('Weight progress added successfully'),
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 186
Text:               {!!weight ? t('View Weight') : t('Add Weight')}
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 198
Text:             label={t('Enter your weight')}
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 205
Text:                 ? t(formik.errors.weight as string)
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 263
Text:                 <Text style={styles.uploadText}>{t('Front image')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 286
Text:                 <Text style={styles.uploadText}>{t('Side image')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 309
Text:                 <Text style={styles.uploadText}>{t('Back image')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 340
Text:               <Text style={styles.deleteFood}>{t('Cancel')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/WeightForm.tsx
Line: 343
Text:               title={t('Add')}
----------------------------------------
File: ./screens/clientflow/ClientHome/WaterIntake.tsx
Line: 68
Text:             <Text style={styles.waterText}>{t('Hydratation')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/DailyIntakeStats.tsx
Line: 30
Text:               {t('Consumed')}
----------------------------------------
File: ./screens/clientflow/ClientHome/DailyIntakeStats.tsx
Line: 36
Text:             <Text style={styles.smallText}>{t('cal')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/DailyIntakeStats.tsx
Line: 56
Text:               {netCaloriesLeft(dailyIntakeGoal, dailyMacroNutrients)}
----------------------------------------
File: ./screens/clientflow/ClientHome/DailyIntakeStats.tsx
Line: 58
Text:             <Text style={styles.calLeftText}>{t('calories')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/DailyIntakeStats.tsx
Line: 67
Text:               {t('Burned')}
----------------------------------------
File: ./screens/clientflow/ClientHome/DailyIntakeStats.tsx
Line: 73
Text:             <Text style={styles.smallText}>{t('cal')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 50
Text:   const [date, setDate] = useState<Moment>(moment());
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 59
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 64
Text:     setDate(moment(date).add(1, 'days'));
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 68
Text:     setDate(moment(date).subtract(1, 'days'));
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 72
Text:   async function getFoodsForClient() {
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 86
Text:   useFocusEffect(
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 88
Text:       getFoodsForClient();
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 92
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 162
Text:                   <Text style={styles.stepText}>{t('Steps')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 176
Text:                   <Text style={styles.stepText}>{t('Activity')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 196
Text:                     {dailyActivity?.totalCaloriesBurned || 0} {t('cal')}
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 207
Text:                 setClickedWeight(undefined);
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 211
Text:                 setClickedWeight(weight);
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 219
Text:               {t('Statistics not available')}
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 223
Text:                 <Text style={styles.goalText}>{t('Goal')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 225
Text:                   {t(
----------------------------------------
File: ./screens/clientflow/ClientHome/index.tsx
Line: 230
Text:                   title={t('Chat Now')}
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 31
Text:             <Text style={styles.heading}>{t('Daily Calories Goal')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 46
Text:                   {netCaloriesLeft(dailyIntake, dailyConsumption) || 0}
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 48
Text:                 <Text style={styles.calLeftText}>{t('calories')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 51
Text:             <Text style={styles.heading}>{t('Daily Nutrients Goal')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 73
Text:                   <Text style={styles.typeText}>{t('Proteins')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 101
Text:                   <Text style={styles.typeText}>{t('Carbs')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 127
Text:                   <Text style={styles.typeText}>{t('Fats')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 137
Text:             title={t('Edit Goals')}
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 146
Text:           <Text style={styles.goalText}>{t('No Goal set yet')}</Text>
----------------------------------------
File: ./screens/clientflow/MyGoals/index.tsx
Line: 147
Text:           <Text style={styles.contactText}>{t('Contact your coach')}</Text>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 36
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 38
Text:       const parts = initialHeight.split(' ');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 43
Text:         setUnit(heightUnit);
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 46
Text:           const [feet, inches] = heightValue.split('.');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 50
Text:           const [meters, centimeters] = heightValue.split('.');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 58
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 59
Text:     setLeftUnitText(unit === 'ft/in' ? "'" : 'm');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 60
Text:     setRightUnitText(unit === 'ft/in' ? "''" : 'cm');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 77
Text:             <Text style={styles.cancelText}>{t('Cancel')}</Text>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 79
Text:           <Text style={styles.headerTitle}>{t('Edit Height')}</Text>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 88
Text:               {t('Save')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 95
Text:             {t('What is your current height?')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 141
Text:                 onPress={() => setUnit('ft/in')}>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 165
Text:                 onPress={() => setUnit('meter')}>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/HeightEditor.tsx
Line: 174
Text:                   {t('Meter')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 14
Text: const {height} = Dimensions.get('window');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 35
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 37
Text:       const parts = initialWeight.split(' ');
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 41
Text:         setUnit(weightUnit);
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 42
Text:         setWeight(weightValue);
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 60
Text:           <Text style={styles.cancelText}>{t('Cancel')}</Text>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 62
Text:         <Text style={styles.headerTitle}>{t('Edit Weight')}</Text>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 69
Text:             {t('Save')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 76
Text:           {t('What is your current weight?')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 85
Text:             initialValue={parseFloat(weight) || 0}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 86
Text:             onValueChange={number => setWeight(number.toString())}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 121
Text:               onPress={() => setUnit('kg')}>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/WeightEditor.tsx
Line: 142
Text:               onPress={() => setUnit('lb')}>
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 43
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 104
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 105
Text:           text2: t('Error while uploading picture'),
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 131
Text:       await Services.UpdateClient(
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 149
Text:         text1: t('Error'),
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 150
Text:         text2: t('Error saving your personal information'),
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 156
Text:   function handleSaveHeight(height: string) {
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 163
Text:   function handleSaveWeight(weight: string) {
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 202
Text:           {t('Account')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 205
Text:           label={t('Name')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 211
Text:             formik.touched.name ? t(formik.errors.name as string) : undefined
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 215
Text:           label={t('Email')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 219
Text:             formik.touched.email ? t(formik.errors.email as string) : undefined
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 231
Text:           {t('Date of birth')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 239
Text:               ? t('Pick Date')
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 240
Text:               : moment(formik.values?.dob).format('MMMM Do, YYYY')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 245
Text:           label={t('Gender')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 255
Text:             label={t('Height')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 263
Text:                 ? t(formik.errors.height as string)
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 277
Text:             label={t('Current Weight')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 283
Text:                 ? t(formik.errors.currentWeight as string)
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 296
Text:           label={t('Goal')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 302
Text:           label={t('Goal Weight')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 308
Text:               ? t(formik.errors.goalWeight as string)
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 314
Text:           label={t('Active Basis')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 326
Text:           title={t('Save Changes')}
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 336
Text:           formik.values.dob ? moment(formik.values.dob).toDate() : new Date()
----------------------------------------
File: ./screens/clientflow/PersonalInformation/index.tsx
Line: 341
Text:           formik.setFieldValue('dob', moment(date).format('YYYY-MM-DD'));
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 103
Text:               <Text style={styles.infoHead}>{t('Age')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 105
Text:                 {moment().diff(moment(client.dob), 'years')} {t('years')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 110
Text:             <Text style={styles.infoHead}>{t('Current Weight')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 114
Text:             <Text style={styles.infoHead}>{t('Goal')}</Text>
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 115
Text:             <Text style={styles.infoDesc}>{t(client?.goal as string)}</Text>
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 122
Text:             label={t('Personal Information')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 123
Text:             title={t('General')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 129
Text:             label={t('My Goals')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 134
Text:             label={t('Subscription')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 141
Text:             label={t('Weight Progress')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 148
Text:             label={t('Coach')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 153
Text:             label={t('Document')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 158
Text:             label={t('Change Password')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 163
Text:             label={t('Language')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 168
Text:             label={t('Terms & Conditions')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 169
Text:             title={t('Help & Support')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 174
Text:             label={t('Help Center')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 185
Text:             label={t('About')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 196
Text:             label={t('Log Out')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 197
Text:             title={t('Log Out')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 206
Text:         topRedTitle={t('Log Out')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 207
Text:         description={t('You are attempting to log out.')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 208
Text:         primaryButtonName={t('Yes, Log Out')}
----------------------------------------
File: ./screens/clientflow/ClientProfile/index.tsx
Line: 209
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 21
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 28
Text:   useLayoutEffect(() => {
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 59
Text:       const resp = await ClientAuthServices.UpdateClient(currentUser._id, {
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 65
Text:         getClient();
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 75
Text:   useEffect(() => {
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 80
Text:         await getClient();
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 102
Text:         {/* <Text style={styles.headingText}>{t('Coach Profile')}</Text> */}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 112
Text:           title={t('Age')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 117
Text:               ? `${moment().diff(moment(coach.dob), 'years')} ${t('Years')}`
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 122
Text:           title={t('Experience')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 125
Text:           data={coach?.experience ? `${coach.experience} ${t('Years')}` : ''}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 128
Text:           title={t('Email')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 134
Text:           title={t('Referal Code')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 140
Text:           title={t('About')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 171
Text:         <Text style={styles.headingText}>{t('Connect to your coach')}</Text>
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 173
Text:           {t('Enter the referral code provided by your coach')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 177
Text:             label={t('Referral Code')}
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 182
Text:                 ? t(formik.errors.referralCode as string)
----------------------------------------
File: ./screens/clientflow/CoachProfile/index.tsx
Line: 189
Text:             title={t('Save')}
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 19
Text:           <Text style={styles.mealName}>{t('Breakfast')}</Text>
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 27
Text:             <Text style={styles.mealName}>{t('Serving')}</Text>
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 31
Text:         <Text style={styles.headingText}>{t('Nutritional Information')}</Text>
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 34
Text:             377<Text style={styles.calUnitText}> {t('cal')}</Text>
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 47
Text:           <Text style={styles.deleteFood}>{t('Delete Food')}</Text>
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 50
Text:           title={t('Save')}
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 59
Text:         topRedTitle={t('Delete Food')}
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 60
Text:         description={t('You are attempting to delete food.')}
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 61
Text:         primaryButtonName={t('Delete')}
----------------------------------------
File: ./screens/clientflow/EditMeal/index.tsx
Line: 62
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 54
Text:           <Text style={styles.title}>{t('Join us as')}:</Text>
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 55
Text:           <Text style={styles.paragraph}>{t('Please choose your role.')}</Text>
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 57
Text:             title={t('Client')}
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 58
Text:             paragraph={t(
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 66
Text:             title={t('Coach')}
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 67
Text:             paragraph={t('Guide clients towards their health goals!')}
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 74
Text:           <Text style={styles.footerText}>{t('Already have an account?')}</Text>
----------------------------------------
File: ./screens/authflow/SelectRole/SelectRole.tsx
Line: 76
Text:             title={t('Login')}
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 34
Text:   useEffect(() => {
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 39
Text:   const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 41
Text:       .matches(/^\d{4}$/, t('OTP must be 4 digits'))
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 42
Text:       .required(t('OTP is required')),
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 71
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 72
Text:           text2: t('Enter a valid OTP'),
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 103
Text:                 {t('Enter OTP code sent to your entered details.')}
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 120
Text:                 <Text style={styles.errorText}>{t(formik.errors.otp)}</Text>
----------------------------------------
File: ./screens/authflow/OTPVerification/OTPVerification.tsx
Line: 124
Text:               title={t('Continue')}
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 22
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 72
Text:                   {t('Please enter your details to pay')}.
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 75
Text:                   label={t('Add Promo Code')}
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 82
Text:                       ? t(formik.errors.promoCode as string)
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 88
Text:                   label={t('Card Name')}
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 95
Text:                       ? t(formik.errors.cardName as string)
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 100
Text:                   label={t('Card Number')}
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 108
Text:                       ? t(formik.errors.cardNumber as string)
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 115
Text:                       label={t('CVV')}
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 122
Text:                           ? t(formik.errors.cvv as string)
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 129
Text:                       label={t('Expiry')}
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 136
Text:                           ? t(formik.errors.expiry as string)
----------------------------------------
File: ./screens/authflow/AddNewCard/AddNewCard.tsx
Line: 145
Text:                 title={t('Continue')}
----------------------------------------
File: ./screens/authflow/PasswordFeedback/PasswordFeedback.tsx
Line: 19
Text:           title={t('Password Recovered')}
----------------------------------------
File: ./screens/authflow/PasswordFeedback/PasswordFeedback.tsx
Line: 20
Text:           paragraph={t(
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 98
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 99
Text:           text2: t('Email is already taken.'),
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 123
Text:   const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 166
Text:         <Text style={styles.title}>{t('Create Account')}</Text>
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 168
Text:           {t('Please fill in all the required fields')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 172
Text:           label={t('Name')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 176
Text:             formik.touched.name ? t(formik?.errors?.name as string) : undefined
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 182
Text:           label={t('Email')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 187
Text:               ? t(formik?.errors?.email as string)
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 195
Text:           label={t('Password')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 203
Text:               ? t(formik?.errors?.password as string)
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 210
Text:           label={t('Confirm Password')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 218
Text:               ? t(formik?.errors?.confirmPassword as string)
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 227
Text:             label={t('Referral Code (optional)')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 232
Text:                 ? t(formik?.errors?.referralCode as string)
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 253
Text:             {t('I accept all')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 255
Text:           <Text style={styles.title2}>{t('Terms & Conditions')}</Text>
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 260
Text:           title={t('Create Account')}
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 269
Text:           <Text style={styles.footerText}>{t('Already have an account?')}</Text>
----------------------------------------
File: ./screens/authflow/SignUp/SignUp.tsx
Line: 271
Text:             title={t('Log in')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 42
Text:   const prompt = t('How active are you on a regular basis?');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 45
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 51
Text:         const resp = await ClientServices.UpdateClient(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 64
Text:           const weightNumber = weight.toString().split(' ')[0];
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 65
Text:           const weightUnit = weight.toString().split(' ')[1];
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 105
Text:             label={t('Sedentary')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 111
Text:             label={t('Lightly Active')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 117
Text:             label={t('Moderately Active')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 123
Text:             label={t('Highly Active')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question7.tsx
Line: 129
Text:           {t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 30
Text:   const prompt = t('How do you define your gender?');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 32
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 37
Text:         const resp = await ClientServices.UpdateClient(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 66
Text:             label={t('Male')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 72
Text:             label={t('Female')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 78
Text:             label={t('Prefer to skip, thanks!')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question2.tsx
Line: 85
Text:           {t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 30
Text:   const prompt = t('What is your current weight?');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 32
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 46
Text:         const resp = await ClientServices.UpdateClient(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 79
Text:             onValueChange={number => setWeight(number)}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 120
Text:               onPress={() => setUnit('kg')}>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 138
Text:               onPress={() => setUnit('lb')}>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question4.tsx
Line: 150
Text:           {t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 32
Text:   const prompt = t('What is your current height?');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 33
Text:   useEffect(() => {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 34
Text:     setLeftUnitText(unit === 'ft/in' ? "'" : 'm');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 35
Text:     setRightUnitText(unit === 'ft/in' ? "''" : 'cm');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 38
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 45
Text:         const resp = await ClientServices.UpdateClient(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 114
Text:               onPress={() => setUnit('ft/in')}>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 133
Text:               onPress={() => setUnit('meter')}>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 139
Text:                 {t('Meter')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question3.tsx
Line: 146
Text:           {t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 35
Text:   const prompt = t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 94
Text:         text1: t('Error'),
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 95
Text:         text2: t("Couldn't upload the images."),
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 101
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 151
Text:             {/* <Text style={styles.textOptional}> ({t('optional')})</Text> */}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 156
Text:                 <Text style={styles.sideText}>{t('Front')}</Text>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 169
Text:                 <Text style={styles.sideText}>{t('Back')}</Text>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 185
Text:               <Text style={styles.sideText}>{t('Side')}</Text>
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question5.tsx
Line: 197
Text:             {t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 30
Text:   const prompt = t('What goals are you pursuing?');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 33
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 39
Text:         const resp = await ClientServices.UpdateClient(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 71
Text:             label={t('Lose Weight')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 77
Text:             label={t('Maintain Weight')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 83
Text:             label={t('Gain Weight')}
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question6.tsx
Line: 90
Text:           {t(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 43
Text:     moment().subtract(15, 'years').toDate(),
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 46
Text:   const prompt = t('What is your current age?');
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 48
Text:   async function addQuestionClient() {
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 54
Text:         const resp = await ClientServices.UpdateClient(
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 58
Text:             dob: moment(currentVal).format('YYYY-MM-DD').toString(),
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 85
Text:             const birthDate = moment(val, 'YYYY').toDate();
----------------------------------------
File: ./screens/authflow/ClientQuestionare/Question1.tsx
Line: 91
Text:           {t(
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 27
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 71
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 72
Text:           text2: t('Invalid password has been entered'),
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 104
Text:                 {t('Enter your previous password')}.
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 109
Text:                 label={t('Password')}
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 114
Text:                     ? t(formik.errors.password as string)
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 121
Text:                 title={t('Forgot Password?')}
----------------------------------------
File: ./screens/authflow/PreviousPassword/PreviousPassword.tsx
Line: 130
Text:               title={t('Continue')}
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 29
Text:   const windowWidth = Dimensions.get('window').width;
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 47
Text:   useEffect(() => {
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 102
Text:         <Text style={styles.title}>{t('Subscription')}</Text>
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 116
Text:           onScroll={Animated.event(
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 157
Text:                     <Text style={styles.largeTitle2}>{t('Month')}</Text>
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 161
Text:                     {t('Plan include')}
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 183
Text:                     {t('Valid for 1 month')}
----------------------------------------
File: ./screens/authflow/Subscription/Subscription.tsx
Line: 187
Text:                     title={t('Pay Now')}
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 30
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 70
Text:           text1: t('Success'),
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 71
Text:           text2: t('Password has been updated.'),
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 89
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 90
Text:           text2: t('Invalid password has been entered.'),
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 123
Text:                 {t('Create a new password that is easy to remember.')}
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 127
Text:                 label={t('Password')}
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 133
Text:                     ? t(formik.errors.password as string)
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 142
Text:                 label={t('Confirm Password')}
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 148
Text:                     ? t(formik.errors.confirmPassword as string)
----------------------------------------
File: ./screens/authflow/ResetPassword/ResetPassword.tsx
Line: 159
Text:               title={t('Continue')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 31
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 64
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 65
Text:           text2: t('Invalid email or password.'),
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 98
Text:             <Text style={styles.title}>{t('Login')}</Text>
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 99
Text:             <Text style={styles.paragraph}>{t('Welcome back !')}</Text>
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 102
Text:               label={t('Email')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 108
Text:                   ? t(formik?.errors?.email as string)
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 116
Text:               label={t('Password')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 123
Text:                   ? t(formik?.errors?.password as string)
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 131
Text:               title={t('Remember me')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 143
Text:               title={t('Login')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 153
Text:               title={t('Forgot Password?')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 162
Text:                 {t(`Don't have an account?`)}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 165
Text:                 title={t('Register Now')}
----------------------------------------
File: ./screens/authflow/Login/Login.tsx
Line: 244
Text:     marginTop: Dimensions.get('screen').height * 0.07,
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 32
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 67
Text:         dob: moment(formik.values.dob).format('YYYY-MM-DD'),
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 78
Text:         setTimeout(() => {
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 118
Text:             <Text style={styles.title}>{t('Profile Setup')}</Text>
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 120
Text:               {t('Please enter your details to setup your profile.')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 135
Text:               {t('DOB')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 153
Text:                   ? t('Pick Date')
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 154
Text:                   : moment(formik.values?.dob).format('MMMM Do, YYYY')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 158
Text:               {formik.touched.dob ? t(formik.errors.dob as string) : undefined}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 163
Text:               label={t('Experience (years)')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 169
Text:                   ? t(formik.errors.experience as string)
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 176
Text:               label={t('About')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 180
Text:                   ? t(formik.errors.about as string)
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 197
Text:               title={t('Done')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 206
Text:               paragraph={t('Your profile created successfully.')}
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 214
Text:           formik.values.dob ? moment(formik.values.dob).toDate() : new Date()
----------------------------------------
File: ./screens/authflow/ProfileSetup/ProfileSetup.tsx
Line: 220
Text:           formik.setFieldValue('dob', moment(date).format('YYYY-MM-DD'));
----------------------------------------
File: ./screens/authflow/PaymentFeedback/PaymentFeedback.tsx
Line: 17
Text:           title={t('Payment Successful')}
----------------------------------------
File: ./screens/authflow/PaymentFeedback/PaymentFeedback.tsx
Line: 18
Text:           paragraph={t('Your subscription is confirmed.')}
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 28
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 84
Text:     setTimeout(() => {
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 109
Text:                 <Text style={styles.title}>{t('Add First Client')}</Text>
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 111
Text:                   {t('Add your first client through email')}
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 115
Text:                   label={t('Name')}
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 121
Text:                       ? t(formik.errors.firstName as string)
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 127
Text:                   label={t('Email')}
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 134
Text:                       ? t(formik.errors.email as string)
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 140
Text:                   title={t('Send Invite')}
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 147
Text:                   title={t('Skip')}
----------------------------------------
File: ./screens/authflow/AddFirstClient/AddFirstClient.tsx
Line: 160
Text:                   paragraph={t(
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 30
Text:   useEffect(() => {
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 40
Text:                 setTimeout(checkInit, 50);
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 43
Text:             checkInit();
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 63
Text:   const buttonName = user?._id ? t('Save Changes') : t('Continue');
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 90
Text:         setTimeout(() => {
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 106
Text:           <Text style={styles.title}>{t('Loading...')}</Text>
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 120
Text:             <Text style={styles.title}>{t('Choose Your Language')}</Text>
----------------------------------------
File: ./screens/authflow/Language/Language.tsx
Line: 127
Text:                 country={t(item.country)}
----------------------------------------
File: ./screens/authflow/Onboard/Onboard.tsx
Line: 26
Text:   const windowWidth = Dimensions.get('window').width;
----------------------------------------
File: ./screens/authflow/Onboard/Onboard.tsx
Line: 74
Text:             onScroll={Animated.event(
----------------------------------------
File: ./screens/authflow/Onboard/Onboard.tsx
Line: 195
Text:               title={t('Get Started')}
----------------------------------------
File: ./screens/authflow/Onboard/Onboard.tsx
Line: 204
Text:               {t('Already have an account?')}
----------------------------------------
File: ./screens/authflow/Onboard/Onboard.tsx
Line: 207
Text:               title={t('Log in')}
----------------------------------------
File: ./screens/authflow/Onboard/Onboard.tsx
Line: 221
Text: const {height, width} = Dimensions.get('window');
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 27
Text: const validationSchema = Yup.object().shape({
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 62
Text:           text1: t('Error'),
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 63
Text:           text2: t('Please enter a valid email.'),
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 94
Text:                 {t('Enter your details associated with this account')}
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 98
Text:                 label={t('Email')}
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 104
Text:                     ? t(formik.errors.email as string)
----------------------------------------
File: ./screens/authflow/EmailVerification/EmailVerification.tsx
Line: 112
Text:               title={t('Continue')}
----------------------------------------
File: ./components/WeightTrackerGraph/WeightTrackerGraph.tsx
Line: 31
Text:       date: moment.unix(+item.createdAt).format('DD/MM'),
----------------------------------------
File: ./components/WeightTrackerGraph/WeightTrackerGraph.tsx
Line: 38
Text:     moment(+item.createdAt).format('DD/MM'),
----------------------------------------
File: ./components/WeightTrackerGraph/WeightTrackerGraph.tsx
Line: 70
Text:             <Text style={styles.weightHeadingText}>{t('Weight Tracking')}</Text>
----------------------------------------
File: ./components/WeightTrackerGraph/WeightTrackerGraph.tsx
Line: 78
Text:             <Text style={styles.weightHeadingText}>{t('Weight Tracking')}</Text>
----------------------------------------
File: ./components/WeightTrackerGraph/WeightTrackerGraph.tsx
Line: 96
Text:                 {t('Weight Tracking')}
----------------------------------------
File: ./components/WeightTrackerGraph/WeightTrackerGraph.tsx
Line: 134
Text:               onPressWeight(found);
----------------------------------------
File: ./components/CoachReview/CoachReviewCard.tsx
Line: 21
Text:           LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
----------------------------------------
File: ./components/CoachReview/CoachReviewCard.tsx
Line: 24
Text:         <Text style={styles.headingText}>{t('Coach reviews')}</Text>
----------------------------------------
File: ./components/ProgressBarComponent/ProgressBarComponent.tsx
Line: 42
Text:           <Text style={styles.typeText}>{t('Proteins')}</Text>
----------------------------------------
File: ./components/ProgressBarComponent/ProgressBarComponent.tsx
Line: 64
Text:           <Text style={styles.typeText}>{t('Carbs')}</Text>
----------------------------------------
File: ./components/ProgressBarComponent/ProgressBarComponent.tsx
Line: 86
Text:           <Text style={styles.typeText}>{t('Fat')}</Text>
----------------------------------------
File: ./components/NutritionProgress/NutritionProgress.tsx
Line: 29
Text:       <Text style={[styles.proteinText, {color: color}]}>{t('Proteins')}</Text>
----------------------------------------
File: ./components/ExerciseComponent/ExerciseComponent.tsx
Line: 32
Text:             <Text style={styles.descText}>{`${time} ${t('mins')}`}</Text>
----------------------------------------
File: ./components/MealDetail/MealDetail.tsx
Line: 31
Text:               {food.calories} {t('cal')}
----------------------------------------
File: ./components/MealDetail/MealDetail.tsx
Line: 34
Text:               {food.quantity} {t('serving')}
----------------------------------------
File: ./components/MealDetail/MealDetail.tsx
Line: 51
Text:             {food.proteins}g {t('Protein')}
----------------------------------------
File: ./components/MealDetail/MealDetail.tsx
Line: 55
Text:             {food.carbs}g {t('Carbs')}
----------------------------------------
File: ./components/MealDetail/MealDetail.tsx
Line: 59
Text:             {food.fats}g {t('Fats')}
----------------------------------------
File: ./components/DocumentComponent/DocumentComponent.tsx
Line: 50
Text:               label: t('Edit'),
----------------------------------------
File: ./components/DocumentComponent/DocumentComponent.tsx
Line: 55
Text:               label: t('Delete'),
----------------------------------------
File: ./components/DocumentComponent/DocumentComponent.tsx
Line: 69
Text:         topRedTitle={t('Delete Document')}
----------------------------------------
File: ./components/DocumentComponent/DocumentComponent.tsx
Line: 70
Text:         description={t('You are attempting to delete the document.')}
----------------------------------------
File: ./components/DocumentComponent/DocumentComponent.tsx
Line: 71
Text:         primaryButtonName={t('Delete')}
----------------------------------------
File: ./components/DocumentComponent/DocumentComponent.tsx
Line: 72
Text:         secondaryButtonText={t('Cancel')}
----------------------------------------
File: ./components/PdfFileContainer/PdfFileContainer.tsx
Line: 39
Text:         const granted = await PermissionsAndroid.request(
----------------------------------------
File: ./components/PdfFileContainer/PdfFileContainer.tsx
Line: 43
Text:           Toast.show({type: 'error', text2: t('Permission Denied')});
----------------------------------------
File: ./components/PdfFileContainer/PdfFileContainer.tsx
Line: 71
Text:           Toast.show({type: 'success', text2: t('Download Complete')});
----------------------------------------
File: ./components/PdfFileContainer/PdfFileContainer.tsx
Line: 82
Text:     return url.split(/[#?]/)[0].split('.').pop().trim();
----------------------------------------
File: ./components/AgePicker/AgePicker.tsx
Line: 12
Text: const {width} = Dimensions.get('window');
----------------------------------------
File: ./components/Modals/GeneralModal/GeneralModal.tsx
Line: 47
Text:             {t('Are you sure?')}
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 35
Text: const reviewSchema = yup.object({
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 85
Text:               {moment(+mealCreatedAt).isSame(moment(), 'day')
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 86
Text:                 ? `Today - ${moment(+mealCreatedAt).format('hh:mm a')}`
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 87
Text:                 : moment(+mealCreatedAt).format('MMM DD - hh:mm a')}
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 92
Text:           label={t('Add Review')}
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 100
Text:             (formik.touched.text && t(formik.errors.text as string)) ||
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 115
Text:             <Text style={styles.cancelText}>{t('Cancel')}</Text>
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 118
Text:             onPress={() => formik.handleSubmit()}
----------------------------------------
File: ./components/Modals/AddReviewModal/AddReviewModal.tsx
Line: 121
Text:             <Text style={styles.saveText}>{t('Save')}</Text>
----------------------------------------
File: ./components/WeightRecord/WeightRecord.tsx
Line: 33
Text:           <Text style={styles.bodySide}>{t('Front')}</Text>
----------------------------------------
File: ./components/WeightRecord/WeightRecord.tsx
Line: 38
Text:           <Text style={styles.bodySide}>{t('Side')}</Text>
----------------------------------------
File: ./components/WeightRecord/WeightRecord.tsx
Line: 43
Text:           <Text style={styles.bodySide}>{t('Back')}</Text>
----------------------------------------
File: ./components/FormInput/index.tsx
Line: 33
Text:   useEffect(() => {
----------------------------------------
File: ./components/FormInput/index.tsx
Line: 61
Text:     ]).start();
----------------------------------------
File: ./components/BottomSheet/CustomBottomSheet.tsx
Line: 40
Text:     bottomSheetModalRef.current?.present();
----------------------------------------
File: ./components/RulerPicker/components/RulerPicker.tsx
Line: 29
Text: const {width: windowWidth} = Dimensions.get('window');
----------------------------------------
File: ./components/RulerPicker/components/RulerPicker.tsx
Line: 181
Text:   useEffect(() => {
----------------------------------------
File: ./components/RulerPicker/components/RulerPicker.tsx
Line: 189
Text:   const scrollHandler = Animated.event(
----------------------------------------
File: ./components/RulerPicker/components/RulerPicker.tsx
Line: 265
Text:     listRef.current?.scrollToOffset({
----------------------------------------
File: ./components/OnboardingCard/OnboardingCard.tsx
Line: 30
Text:         {t(label)}
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 69
Text:           <Text style={styles.detailText}>{t('Details')}</Text>
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 84
Text:           {totalCalories} <Text style={{fontSize: 14}}>{t('cal')}</Text>
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 95
Text:             {t('Protein')}
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 109
Text:             {t('Carbs')}
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 123
Text:             {t('Fat')}
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 137
Text:           <Text style={styles.name}>{t('Reviews')}</Text>
----------------------------------------
File: ./components/MealInfo/MealInfo.tsx
Line: 143
Text:           <Text style={styles.reviewedText}>{t('Reviewed')}</Text>
----------------------------------------
File: ./components/SelectOptionModal/SelectOptionModal.tsx
Line: 72
Text:                   <Text style={styles.selectOptionText}>{t(item)}</Text>
----------------------------------------
File: ./components/Feedback/Feedback.tsx
Line: 34
Text:         title={t('Continue')}
----------------------------------------
File: ./components/ClientHomeCalory/ClientHomeCalory.tsx
Line: 51
Text:           <Text style={styles.headingText}>{t(value)}</Text>
----------------------------------------
File: ./components/Header/Header.tsx
Line: 67
Text:         <ListItem.Title style={styles.title}>{t('Hello')} 👋</ListItem.Title>
----------------------------------------
File: ./components/SearchBar/SearchBar.tsx
Line: 28
Text:         placeholder={t('Search')}
----------------------------------------
File: ./components/CalendarStrip/CalendarStrip.tsx
Line: 39
Text:         {moment(date.format()).format('dddd')},{' '}
----------------------------------------
File: ./components/CalendarStrip/CalendarStrip.tsx
Line: 40
Text:         {moment(date.format()).format('LL')}
----------------------------------------
File: ./components/CalendarStrip/CalendarStrip.tsx
Line: 44
Text:         disabled={!moment(date).isBefore(moment().startOf('day'))}>
----------------------------------------
File: ./components/CalendarStrip/CalendarStrip.tsx
Line: 49
Text:             !moment(date).isBefore(moment().startOf('day'))
----------------------------------------
File: ./components/CalendarStrip/CalendarStrip.tsx
Line: 63
Text:           setDate(moment(date));
----------------------------------------
File: ./i18n/i18n.ts
Line: 12
Text: i18n.use(initReactI18next).init({
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 92
Text:       dispatch(deleteClient({_id: clientID}));
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 93
Text:       const resp = await ClientServices.DeleteClient(clientID);
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 97
Text:           text2: t('Client deleted'),
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 105
Text:         text1: t('Error'),
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 106
Text:         text2: t('Error while deleting the client'),
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 113
Text:       dispatch(toggleArchiveClient({_id: id}));
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 115
Text:       await ClientServices.ArchiveClient(id, status);
----------------------------------------
File: ./hooks/models/useClients.ts
Line: 123
Text:       const resp = await ClientServices.GetArchivedClient(clientId);
----------------------------------------
File: ./hooks/models/useMeals.ts
Line: 27
Text:       const today = date.format('YYYY-MM-DD');
----------------------------------------
File: ./hooks/models/useMeals.ts
Line: 31
Text:         const resp = await MealServices.GetMealsByClient(user._id, today);
----------------------------------------
File: ./hooks/models/useMeals.ts
Line: 65
Text:       showErrorToast(t('Something went wrong in creating meal'));
----------------------------------------
File: ./hooks/models/useMeals.ts
Line: 91
Text:         showErrorToast(t('Make sure your internet is working.'));
----------------------------------------
File: ./hooks/models/useMeals.ts
Line: 93
Text:         showErrorToast(t('Something went wrong in creating meal'));
----------------------------------------
File: ./hooks/models/useStatistics.ts
Line: 17
Text:       const resp = await WeightServices.GetWeightByClient(user._id);
----------------------------------------
File: ./hooks/models/useStatistics.ts
Line: 27
Text:     const today = moment().format('YYYY-MM-DD');
----------------------------------------
File: ./hooks/models/useChats.ts
Line: 15
Text:   // const socket = getSocket();
----------------------------------------
File: ./hooks/models/useChats.ts
Line: 21
Text:   // useEffect(() => {
----------------------------------------
File: ./hooks/models/useChats.ts
Line: 26
Text:   //     socket?.disconnect();
----------------------------------------
File: ./hooks/models/useChats.ts
Line: 30
Text:   // useEffect(() => {
----------------------------------------
File: ./hooks/exec/useFetchers.ts
Line: 25
Text:   useEffect(() => {
----------------------------------------
File: ./hooks/exec/useFetchers.ts
Line: 30
Text:         fetchClient(),
----------------------------------------
File: ./hooks/exec/useFetchers.ts
Line: 31
Text:         fetchMeals(moment()),
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 32
Text:   useEffect(() => {
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 34
Text:       initializeSocket(user._id);
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 38
Text:       disconnectSocket(); // Cleanup socket on component unmount
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 42
Text:   useEffect(() => {
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 47
Text:   // useEffect(() => {
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 53
Text:   //       dispatch(setStepCount(stepCount));
----------------------------------------
File: ./navigation/RootNavigation.tsx
Line: 81
Text:   //     dispatch(setStepCount(stepsToday));
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 110
Text:           headerTitle: t('Notifications'),
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 118
Text:           headerTitle: t('Archive'),
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 126
Text:           headerTitle: t('Select Language'),
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 134
Text:           headerTitle: t('Change Password'),
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 140
Text:         options={{headerShown: true, headerTitle: t('Forgot Password')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 145
Text:         options={{headerShown: true, headerTitle: t('Reset Password')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 150
Text:         options={{headerShown: true, headerTitle: t('Forgot Password')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 156
Text:         options={{headerShown: true, headerTitle: t('Terms & Conditions')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 161
Text:         options={{headerShown: true, headerTitle: t('About')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 166
Text:         options={{headerShown: true, headerTitle: t('Personal Info')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 172
Text:         options={{headerShown: true, headerTitle: t('Subscription')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 177
Text:         options={{headerShown: true, headerTitle: t('Subscription')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 182
Text:         options={{headerShown: true, headerTitle: t('Payment')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 187
Text:         options={{headerShown: true, headerTitle: t('Document')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 192
Text:         options={{headerShown: true, headerTitle: t('Add New Document')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 198
Text:         options={{headerShown: true, headerTitle: t('Edit Document')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 203
Text:         options={{headerShown: true, headerTitle: t('Help Center')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 209
Text:         options={{headerShown: true, headerTitle: t('My QR')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 221
Text:         options={{headerShown: true, headerTitle: t('History')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 233
Text:         options={{headerShown: true, headerTitle: t('Daily Goals')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 239
Text:         options={{headerShown: true, headerTitle: t('Weight Progress')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 245
Text:         options={{headerShown: true, headerTitle: t('Add Client')}}
----------------------------------------
File: ./navigation/HomeStack/HomeStackNavigator.tsx
Line: 251
Text:         options={{headerShown: true, headerTitle: t('Details')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 123
Text:           headerTitle: t(route.route.params.mealTitle),
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 127
Text:         //     title: t(route?.route?.params?.isNew ? 'Add Card' : 'Checkout'),
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 137
Text:           headerTitle: t('Search Food'),
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 152
Text:         options={{headerShown: true, headerTitle: t('Subscription')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 162
Text:         options={{headerShown: true, headerTitle: t('Personal Info')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 168
Text:         options={{headerShown: true, headerTitle: t('My Goals')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 174
Text:         options={{headerShown: true, headerTitle: t('Personal Info')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 179
Text:         options={{headerShown: true, headerTitle: t('Document')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 184
Text:         options={{headerShown: true, headerTitle: t('Add New Document')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 190
Text:         options={{headerShown: true, headerTitle: t('Add New Document')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 195
Text:         options={{headerShown: true, headerTitle: t('Details')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 200
Text:         options={{headerShown: true, headerTitle: t('Weight Progress')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 212
Text:           headerTitle: t('Select Language'),
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 220
Text:           headerTitle: t('Change Password'),
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 226
Text:         options={{headerShown: true, headerTitle: t('Forgot Password')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 231
Text:         options={{headerShown: true, headerTitle: t('Forgot Password')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 236
Text:         options={{headerShown: true, headerTitle: t('Reset Password')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 245
Text:         options={{headerShown: true, headerTitle: t('Terms & Conditions')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 250
Text:         options={{headerShown: true, headerTitle: t('Help Center')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 255
Text:         options={{headerShown: true, headerTitle: t('About')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 261
Text:         options={{headerShown: true, headerTitle: t('Activity')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 267
Text:         options={{headerShown: true, headerTitle: t('Add Exercise')}}
----------------------------------------
File: ./navigation/ClientStackNavigator/ClientStackNavigator.tsx
Line: 273
Text:         options={{headerShown: false, headerTitle: t('Scan Code')}}
----------------------------------------
File: ./navigation/ClientBottomNavigation/ClientBottomNavigation.tsx
Line: 86
Text:         options={{tabBarLabel: t('Home')}}
----------------------------------------
File: ./navigation/ClientBottomNavigation/ClientBottomNavigation.tsx
Line: 117
Text:           tabBarLabel: t('Message'),
----------------------------------------
File: ./navigation/ClientBottomNavigation/ClientBottomNavigation.tsx
Line: 123
Text:         options={{tabBarLabel: t('Profile')}}
----------------------------------------
File: ./navigation/AuthNavigation/AuthNavigation.tsx
Line: 90
Text:         options={{headerTitle: t('Login')}}
----------------------------------------
File: ./navigation/AuthNavigation/AuthNavigation.tsx
Line: 95
Text:         options={{headerTitle: t('Sign Up')}}
----------------------------------------
File: ./navigation/AuthNavigation/AuthNavigation.tsx
Line: 101
Text:         options={{headerShown: true, headerTitle: t('Forgot Password')}}
----------------------------------------
File: ./navigation/AuthNavigation/AuthNavigation.tsx
Line: 106
Text:         options={{headerShown: true, headerTitle: t('Reset Password')}}
----------------------------------------
File: ./navigation/AuthNavigation/AuthNavigation.tsx
Line: 111
Text:         options={{headerShown: true, headerTitle: t('Forgot Password')}}
----------------------------------------
File: ./navigation/BottomNavigation/BottomNavigation.tsx
Line: 104
Text:         options={{tabBarLabel: t('Home')}}
----------------------------------------
File: ./navigation/BottomNavigation/BottomNavigation.tsx
Line: 109
Text:         options={{tabBarLabel: t('Clients')}}
----------------------------------------
File: ./navigation/BottomNavigation/BottomNavigation.tsx
Line: 158
Text:           tabBarLabel: t('Message'),
----------------------------------------
File: ./navigation/BottomNavigation/BottomNavigation.tsx
Line: 164
Text:         options={{tabBarLabel: t('Profile')}}
----------------------------------------
File: ./services/socketService.ts
Line: 35
Text:         createdAt: moment(msg.createdAt).toDate(),
----------------------------------------
File: ./services/socketService.ts
Line: 46
Text:     socket.disconnect();
----------------------------------------
File: ./services/DocumentServices.ts
Line: 3
Text: async function UploadDocument(data: any) {
----------------------------------------
File: ./services/DocumentServices.ts
Line: 14
Text: async function CreateDocument(
----------------------------------------
File: ./services/DocumentServices.ts
Line: 29
Text: async function UpdateDocument(
----------------------------------------
File: ./services/DocumentServices.ts
Line: 51
Text: async function DeleteDocument(documentId: string) {
----------------------------------------
File: ./services/MealServices.ts
Line: 21
Text: async function GetMealsByClient(clientId: string, date?: string) {
----------------------------------------
File: ./services/ClientAuthServices.ts
Line: 74
Text: async function UpdateClient(clientId: string, client: any) {
----------------------------------------
File: ./services/ChatServices.ts
Line: 3
Text: async function GetUserList(
----------------------------------------
File: ./services/WaterService.ts
Line: 14
Text: async function GetWaterByClient(clientId: string) {
----------------------------------------
File: ./services/WeightServices.ts
Line: 4
Text: async function AddWeight(weight: any) {
----------------------------------------
File: ./services/WeightServices.ts
Line: 14
Text: async function UploadImagesForWeight(payload: any) {
----------------------------------------
File: ./services/WeightServices.ts
Line: 27
Text: async function GetWeight() {
----------------------------------------
File: ./services/WeightServices.ts
Line: 36
Text: async function GetWeightByClient(clientId: string) {
----------------------------------------
File: ./services/ClientServices.ts
Line: 16
Text: async function DeleteClient(clientId: string) {
----------------------------------------
File: ./services/ClientServices.ts
Line: 25
Text:     url: `/client/goal-by-date/${id}?date=${moment().format()}`,
----------------------------------------
File: ./services/ClientServices.ts
Line: 60
Text: async function GetArchivedClient(coachId: string) {
----------------------------------------
File: ./services/ClientServices.ts
Line: 67
Text: async function ArchiveClient(clientId: String, archive: boolean) {
----------------------------------------
File: ./services/ClientServices.ts
Line: 102
Text: async function UpdateClient(clientId: string, payload: IUpdateClient) {
----------------------------------------
File: ./services/AuthServices.js
Line: 41
Text:   async UpdateClient(user, userId) {
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 13
Text:         text1: t('Error'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 14
Text:         text2: t('Make sure your internet is working.'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 22
Text:         text1: t('Error'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 23
Text:         text2: t('Something went wrong.'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 32
Text:         text1: t('Error'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 41
Text:         text1: t('Error'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 42
Text:         text2: t('Make sure your internet is working.'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 48
Text:       showErrorToast(t('Something went wrong.'));
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 55
Text:       text1: t('Error'),
----------------------------------------
File: ./utilities/useApiHandler.ts
Line: 70
Text:     return t('Something went wrong...');
----------------------------------------
File: ./utilities/theme.ts
Line: 6
Text:     ...Platform.select({
----------------------------------------
File: ./utilities/theme.ts
Line: 35
Text: const screenHeight = Dimensions.get('window').height;
----------------------------------------
File: ./utilities/theme.ts
Line: 39
Text:   deviceWidth: Dimensions.get('screen').width,
----------------------------------------
File: ./utilities/theme.ts
Line: 40
Text:   deviceHeight: Dimensions.get('screen').height,
----------------------------------------
--------------------------------
SOme additional Text fixes:
File: Slider.ts 
Line: 3 - 23
------------------
File : SignUp.tsx
Line : Text between line 123 to 139
-------------------
