import {
  Keyboard,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {SafeAreaView} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';
import OTPInputView from '@twotalltotems/react-native-otp-input';
import {Button} from '../../../components';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {AxiosError} from 'axios';
import AuthServices from '../../../services/AuthServices';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import useApiHandler from '../../../utilities/useApiHandler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Platform} from 'react-native';

type Props = NativeStackScreenProps<AuthStackParamList, 'OTPVerification'>;
const Services = new AuthServices();

const OTPVerification: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const inputRef = useRef<OTPInputView>(null);
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    if (inputRef.current) inputRef?.current?.focusField(0);
  }, []);
  const {email} = route.params;
  // Define validation schema using Yup
  const validationSchema = Yup.object().shape({
    otp: Yup.string()
      .matches(/^\d{4}$/, t('OTP must be 4 digits'))
      .required(t('OTP is required')),
  });

  const handleVerifyOTP = async () => {
    try {
      setIsLoading(true);
      const resp = await Services.VerifyOtp({
        email: email,
        otp: formik.values.otp,
      });
      if (resp.status == 200) {
        navigation.replace('ResetPassword', {
          email: email,
          otp: formik.values.otp,
          reset_channel: 'forgotPassword',
        });
      }
    } catch (error) {
      const err = error as AxiosError;
      const errResp = err.response?.data as Partial<{
        message?: string;
        statusCode?: number;
      }>;
      console.log('Error while verifying OTP', err.response?.data);

      // INVALID OTP
      if (errResp?.statusCode === 400 && errResp?.message === 'Invalid OTP') {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Enter a valid OTP'),
        });
        return;
      }
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize useFormik hook
  const formik = useFormik({
    initialValues: {
      otp: '',
    },
    validationSchema: validationSchema,
    onSubmit: handleVerifyOTP,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={20}
        enableOnAndroid={true}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.container}>
            <View>
              <Text style={styles.title}>
                {t('Enter OTP code sent to your entered details.')}
              </Text>
              <OTPInputView
                ref={inputRef}
                style={styles.otpView}
                pinCount={4}
                autoFocusOnLoad={false}
                codeInputFieldStyle={{
                  ...styles.codeInputFieldStyle,
                  color: formik.values.otp
                    ? theme.lightColors?.primary
                    : theme.lightColors?.black,
                }}
                onCodeFilled={code => formik.setFieldValue('otp', code)}
                onCodeChanged={code => formik.setFieldValue('otp', code)}
              />
              {formik.errors.otp && (
                <Text style={styles.errorText}>{t(formik.errors.otp)}</Text>
              )}
            </View>
            <Button
              title={t('Continue')}
              onPress={formik.handleSubmit}
              disabled={!formik.isValid || isLoading}
              loading={isLoading}
              containerStyle={{marginBottom: 10, marginHorizontal: 4}}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default OTPVerification;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'space-between',
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    justifyContent: 'space-between',
    paddingBottom: Platform.OS === 'android' ? 40 : 20,
  },
  title: {
    fontSize: 14,
    color: `${theme.lightColors?.secondary}60`,
    fontFamily: Fonts.regular,
    paddingBottom: 67,
  },
  borderStyleBase: {},
  borderStyleHighLighted: {
    borderColor: '#03DAC6',
  },
  codeInputFieldStyle: {
    width: 62,
    height: 48,
    borderWidth: 1,
    borderRadius: 10,
  },
  otpView: {
    height: 48,
    marginHorizontal: 24,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 7,
    marginLeft: 30,
    fontFamily: Fonts.medium,
  },
});
