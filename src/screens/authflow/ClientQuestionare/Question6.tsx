import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import PNGIcons from '../../../assets/pngIcons';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import {AxiosError} from 'axios';
import ClientServices from '../../../services/ClientServices';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTranslation} from 'react-i18next';
import OnboardingCard from '../../../components/OnboardingCard/OnboardingCard';

type Props = NativeStackScreenProps<AuthStackParamList, 'Question2'>;
const Question6: React.FC<Props> = ({navigation}) => {
  const [selectedGoal, setSelectedGoal] = useState<
    'Lose Weight' | 'Maintain Weight' | 'Gain Weight' | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();

  const prompt = t('What goals are you pursuing?');

  // ADD QUESTION NO. 6
  async function addQuestionClient() {
    setIsLoading(true);
    const userData = await AsyncStorage.getItem('userData');

    if (userData) {
      try {
        const resp = await ClientServices.UpdateClient(
          JSON.parse(userData).id,
          {
            step: 6,
            goal: selectedGoal,
          },
        );

        // SUCCESS
        if (resp.status === 200) {
          setIsLoading(false);
          navigation.navigate('Question7');
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log(err.response);
      } finally {
        setIsLoading(false);
      }
    }
  }

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <QuestionareHeader progress={0.852} />
      <View style={{justifyContent: 'space-between', flex: 1}}>
        <Text style={[styles.headingText, {width: '70%', textAlign: 'center'}]}>
          {prompt}
        </Text>
        <View>
          <OnboardingCard
            label={t('Lose Weight')}
            isActive={selectedGoal === 'Lose Weight'}
            onPressItem={() => setSelectedGoal('Lose Weight')}
            isMargin
          />
          <OnboardingCard
            label={t('Maintain Weight')}
            isActive={selectedGoal === 'Maintain Weight'}
            onPressItem={() => setSelectedGoal('Maintain Weight')}
            isMargin
          />
          <OnboardingCard
            label={t('Gain Weight')}
            isActive={selectedGoal === 'Gain Weight'}
            onPressItem={() => setSelectedGoal('Gain Weight')}
          />
        </View>

        <Text style={styles.infoText}>
          {t(
            'We use this information to calculate and provide you with daily personalized recommendations.',
          )}
        </Text>
      </View>
      <TouchableOpacity
        style={{marginVertical: 35, alignSelf: 'flex-end'}}
        disabled={!selectedGoal || isLoading}
        onPress={addQuestionClient}>
        {!isLoading ? (
          <Image
            source={!selectedGoal ? PNGIcons.NextDisabled : PNGIcons.Next}
            style={styles.nextIcon}
          />
        ) : (
          <ActivityIndicator
            size={'small'}
            style={styles.activityIndicator}
            color={theme.lightColors?.primary}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default Question6;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  infoText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },

  selectedGoal: {
    borderColor: theme.lightColors?.primary,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
});
