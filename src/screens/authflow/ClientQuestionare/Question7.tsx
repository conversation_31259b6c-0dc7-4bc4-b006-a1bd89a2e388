import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import PNGIcons from '../../../assets/pngIcons';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import {useDispatch} from 'react-redux';
import {setUser} from '../../../store/userSlice';
import ClientServices from '../../../services/ClientServices';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AxiosError} from 'axios';
import {
  IGoalCalculator,
  goalCalculator,
  kgToLbs,
} from '../../../utilities/app.utils';
import {useTranslation} from 'react-i18next';
import OnboardingCard from '../../../components/OnboardingCard/OnboardingCard';

type Props = NativeStackScreenProps<AuthStackParamList, 'Question2'>;
const Question7: React.FC<Props> = ({navigation}) => {
  const [selectedActivityLevel, setSelectedActivityLevel] = useState<
    | 'Sedentary'
    | 'Lightly Active'
    | 'Moderately Active'
    | 'Highly Active'
    | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const {t} = useTranslation();

  const prompt = t('How active are you on a regular basis?');

  // ADD QUESTION NO. 7
  async function addQuestionClient() {
    setIsLoading(true);
    const userData = await AsyncStorage.getItem('userData');

    if (userData) {
      try {
        const resp = await ClientServices.UpdateClient(
          JSON.parse(userData).id,
          {
            step: 7,
            howActive: selectedActivityLevel,
          },
        );

        // SUCCESS
        if (resp.status === 200) {
          const {weight, goal, gender, howActive} =
            resp.data as IGoalCalculator;

          const weightNumber = weight.toString().split(' ')[0];
          const weightUnit = weight.toString().split(' ')[1];
          const formattedWeight =
            weightUnit === 'kg' ? kgToLbs(Number(weightNumber)) : weightNumber;

          let goalPayload = {
            ...goalCalculator({
              weight: formattedWeight as number,
              goal,
              gender,
              howActive,
            }),
            client: JSON.parse(userData).id,
          };

          if (!resp?.data?.coach) {
            await ClientServices.SetClientGoal(goalPayload);
          }
          const updatedUserData = {...resp.data, id: resp.data._id};
          AsyncStorage.setItem('userData', JSON.stringify(updatedUserData));
          dispatch(setUser(updatedUserData));
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log(err.response?.data);
      } finally {
        setIsLoading(false);
      }
    }
  }

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <QuestionareHeader progress={1} />
      <View style={{justifyContent: 'space-between', flex: 1}}>
        <Text style={[styles.headingText, {width: '70%', textAlign: 'center'}]}>
          {prompt}
        </Text>
        <View>
          <OnboardingCard
            label={t('Sedentary')}
            isActive={selectedActivityLevel === 'Sedentary'}
            onPressItem={() => setSelectedActivityLevel('Sedentary')}
            isMargin
          />
          <OnboardingCard
            label={t('Lightly Active')}
            isActive={selectedActivityLevel === 'Lightly Active'}
            onPressItem={() => setSelectedActivityLevel('Lightly Active')}
            isMargin
          />
          <OnboardingCard
            label={t('Moderately Active')}
            isActive={selectedActivityLevel === 'Moderately Active'}
            onPressItem={() => setSelectedActivityLevel('Moderately Active')}
            isMargin
          />
          <OnboardingCard
            label={t('Highly Active')}
            isActive={selectedActivityLevel === 'Highly Active'}
            onPressItem={() => setSelectedActivityLevel('Highly Active')}
          />
        </View>
        <Text style={styles.infoText}>
          {t(
            'We use this information to calculate and provide you with daily personalized recommendations.',
          )}
        </Text>
      </View>

      {/* NEXT ARROW */}
      <TouchableOpacity
        style={{marginVertical: 35, alignSelf: 'flex-end'}}
        disabled={!selectedActivityLevel || isLoading}
        onPress={addQuestionClient}>
        {!isLoading ? (
          <Image
            source={
              !selectedActivityLevel ? PNGIcons.NextDisabled : PNGIcons.Next
            }
            style={styles.nextIcon}
          />
        ) : (
          <ActivityIndicator
            size={'small'}
            style={styles.activityIndicator}
            color={theme.lightColors?.primary}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default Question7;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  infoText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  genderText: {
    color: theme.lightColors?.black,
    textAlign: 'center',
    fontFamily: Fonts.medium,
    fontSize: 14,
  },
  genderContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    paddingVertical: 25,
  },
  selectedActivityLevel: {
    borderColor: theme.lightColors?.primary,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
});
