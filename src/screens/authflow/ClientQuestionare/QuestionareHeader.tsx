import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {BackArrow} from '../../../assets/svgIcons';
import * as Progress from 'react-native-progress';
import {useNavigation} from '@react-navigation/native';
import {Fonts, theme} from '../../../utilities/theme';

interface props {
  progress: number;
  showSkip?: boolean;
  onPressSkip?: () => void;
}

const QuestionareHeader: React.FC<props> = ({
  progress,
  showSkip,
  onPressSkip,
}) => {
  const navigation = useNavigation();
  return (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
        hitSlop={styles.hitSlop}>
        <BackArrow stroke={theme.lightColors?.secondary} />
      </TouchableOpacity>
      <View>
        <Progress.Bar
          progress={progress}
          height={8}
          width={183}
          animated
          unfilledColor="#ECECEC"
          color={theme.lightColors?.primary}
          borderColor="transparent"
          borderRadius={20}
          style={{borderRadius: 20}}
          animationType="spring"
        />
      </View>
      {showSkip ? (
        <TouchableOpacity onPress={onPressSkip} hitSlop={8}>
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      ) : (
        <View style={{width: 30}} />
      )}
    </View>
  );
};

export default QuestionareHeader;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 24,
  },
  backButton: {
    width: 30,
  },

  hitSlop: {
    left: 15,
    right: 15,
    bottom: 15,
    top: 15,
  },
  skipText: {
    fontFamily: Fonts.regular,
    color: '#2C2C2E80',
    fontSize: 14,
  },
});
