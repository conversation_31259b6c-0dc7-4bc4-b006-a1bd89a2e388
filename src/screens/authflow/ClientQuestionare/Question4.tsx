import {
  ActivityIndicator,
  Image,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import PNGIcons from '../../../assets/pngIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ClientServices from '../../../services/ClientServices';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import {RulerPicker} from '../../../components/RulerPicker';
type Props = NativeStackScreenProps<AuthStackParamList, 'Question3'>;

const Question4: React.FC<Props> = ({navigation}) => {
  const [unit, setUnit] = useState<'kg' | 'lb'>('kg');
  const [isLoading, setIsLoading] = useState(false);
  const [weight, setWeight] = useState('');
  const {t} = useTranslation();

  const prompt = t('What is your current weight?');

  async function addQuestionClient() {
    setIsLoading(true);
    const userData = await AsyncStorage.getItem('userData');
    console.log('userData', userData);

    const payload = {
      step: 4,
      weight: Number(weight).toFixed(0).toString() + ' ' + unit,
    };

    if (userData) {
      try {
        console.log(payload);

        const resp = await ClientServices.UpdateClient(
          JSON.parse(userData).id,
          payload,
        );

        // SUCCESS
        if (resp.status === 200) {
          setIsLoading(false);
          navigation.navigate('Question5');
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as AxiosError;
        console.log(err.response);
      }
    }
  }

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <QuestionareHeader progress={0.568} />
      <View style={{justifyContent: 'space-between', flex: 1}}>
        <Text style={[styles.headingText, {width: '70%', textAlign: 'center'}]}>
          {prompt}
        </Text>
        <View>
          <RulerPicker
            min={0}
            max={300}
            step={1}
            fractionDigits={0}
            initialValue={0}
            onValueChange={number => setWeight(number)}
            onValueChangeEnd={number => console.log(number)}
            unit={unit}
            indicatorColor={theme.lightColors?.black}
            stepWidth={4}
            indicatorHeight={130}
            decelerationRate={'fast'}
            gapBetweenSteps={3}
            shortStepHeight={24}
            shortStepColor="#D7D8D9"
            longStepHeight={70}
            longStepColor="#BABBBE"
            height={200}
            unitTextStyle={{
              fontSize: 40,
              color: '#676C75',
              marginTop: 30,
            }}
            valueTextStyle={{
              fontSize: 80,
              color: theme.lightColors?.black,
              marginTop: Platform.OS === 'ios' ? 40 : -10,
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              gap: 12,
              marginTop: 32,
            }}>
            <TouchableOpacity
              style={[
                styles.unitContainer,
                {
                  backgroundColor:
                    unit === 'kg'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setUnit('kg')}>
              <Text
                style={{
                  color: unit === 'kg' ? theme.lightColors?.white : '#969696',
                }}>
                kg
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.unitContainer,
                {
                  backgroundColor:
                    unit === 'lb'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setUnit('lb')}>
              <Text
                style={{
                  color: unit === 'lb' ? theme.lightColors?.white : '#969696',
                }}>
                lb
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.infoText}>
          {t(
            'We use this information to calculate and provide you with daily personalized recommendations.',
          )}
        </Text>
      </View>
      <TouchableOpacity
        style={{marginVertical: 35, alignSelf: 'flex-end'}}
        disabled={!weight}
        onPress={addQuestionClient}>
        {!isLoading ? (
          <Image
            source={!weight ? PNGIcons.NextDisabled : PNGIcons.Next}
            style={styles.nextIcon}
          />
        ) : (
          <ActivityIndicator
            size={'small'}
            style={styles.activityIndicator}
            color={theme.lightColors?.primary}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default Question4;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  infoText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  unitContainer: {
    color: theme.lightColors?.black,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
});
