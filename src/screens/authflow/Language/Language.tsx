import {SafeAreaView, StyleSheet, Text, View} from 'react-native';
import React, {useState, useEffect} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import LangItem from './LangItem';
import {LANGUAGE} from '../../../constants/Languages';
import {Button} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {useAppDispatch, useAppSelector} from '../../../store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {setFirstLaunch} from '../../../store/userSlice';
import {LanguageInitial, setLanguage} from '../../../store/LanguagesSlice';
import {useTranslation} from 'react-i18next';
import i18n from '../../../i18n/i18n'; // Import your i18n configuration

type Props = NativeStackScreenProps<AuthStackParamList, 'Language'>;

const Language: React.FC<Props> = ({navigation, route}) => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(state => state.language.selectedLanguage);
  const user = useAppSelector(state => state.user.user);
  const {t} = useTranslation();

  const [selectedLanguage, setSelectedLanguage] =
    useState<LanguageInitial>(language);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize component state
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Ensure i18n is ready before proceeding
        if (!i18n.isInitialized) {
          await new Promise(resolve => {
            const checkInit = () => {
              if (i18n.isInitialized) {
                resolve(true);
              } else {
                setTimeout(checkInit, 50);
              }
            };
            checkInit();
          });
        }

        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing language:', error);
        setIsInitialized(true); // Continue anyway
      }
    };

    initializeLanguage();
  }, []);

  const handleChangeLang = (lang: LanguageInitial) => {
    if (!isLoading) {
      setSelectedLanguage(lang);
    }
  };

  const buttonName = user?._id ? t('Save Changes') : t('Continue');

  const handleContinue = async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);

      // Update Redux store
      dispatch(setLanguage(selectedLanguage as LanguageInitial));

      // Change i18n language and wait for it to complete
      await i18n.changeLanguage(selectedLanguage);

      // Store language preference
      await AsyncStorage.setItem('selectedLanguage', selectedLanguage);

      // Navigate based on user status
      if (user?._id) {
        // User is logged in, go back
        navigation.goBack();
      } else {
        // First time user
        await AsyncStorage.setItem('appLaunched', 'true');
        dispatch(setFirstLaunch(true));

        // Small delay to ensure language change is processed
        setTimeout(() => {
          navigation.navigate('Onboard');
        }, 100);
      }
    } catch (error) {
      console.error('Error in handleContinue:', error);
      // Handle error gracefully
      setIsLoading(false);
    }
  };

  // Don't render until initialized
  if (!isInitialized) {
    return (
      <SafeAreaView style={styles.screenWrapper}>
        <View style={[styles.container, {justifyContent: 'center'}]}>
          <Text style={styles.title}>{t('Loading...')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <View>
          {/* TITLE */}
          {user?._id ? (
            <View style={{marginTop: 20}} />
          ) : (
            <Text style={styles.title}>{t('Choose Your Language')}</Text>
          )}
          {/*  */}
          {LANGUAGE.map((item, index) => {
            return (
              <LangItem
                key={item.id}
                country={t(item.country)}
                icon={<item.flag />}
                selectedLanguage={selectedLanguage}
                setSelectedLanguage={handleChangeLang}
                lang={item.lang}
                showDivider={LANGUAGE.length - 1 === index}
              />
            );
          })}
        </View>
        <Button
          title={buttonName}
          onPress={handleContinue}
          loading={isLoading}
          disabled={isLoading}
        />
      </View>
    </SafeAreaView>
  );
};

export default Language;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: 32,
  },
  title: {
    fontSize: 16,
    color: theme.lightColors?.secondary,
    textAlign: 'center',
    paddingTop: 16,
    marginBottom: 32,
    fontFamily: Fonts.semiBold,
  },
});
