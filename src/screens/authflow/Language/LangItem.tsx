import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {FC} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Check, CheckFill} from '../../../assets/svgIcons';
import {LanguageInitial} from '../../../store/LanguagesSlice';

interface Props {
  country: string;
  icon: Element;
  selectedLanguage?: string;
  setSelectedLanguage: (country: LanguageInitial) => void;
  lang: string;
  showDivider?: boolean;
}

const LangItem: FC<Props> = ({
  country,
  icon,
  selectedLanguage,
  setSelectedLanguage,
  lang,
  showDivider,
}) => {
  return (
    <TouchableOpacity
      disabled={selectedLanguage === lang}
      style={styles.container}
      onPress={() => setSelectedLanguage(lang as LanguageInitial)}>
      <View style={styles.innerContainer}>
        <>{icon}</>
        <Text style={styles.title}>{country}</Text>
        {selectedLanguage === lang ? <CheckFill /> : null}
      </View>
      {showDivider ? null : <View style={styles.divider} />}
    </TouchableOpacity>
  );
};

export default LangItem;

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
  },
  title: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    marginLeft: 8,
    flex: 1,
    fontFamily: Fonts.regular,
  },
  divider: {
    height: 1,
    backgroundColor: theme.lightColors?.grey2,
    width: '100%',
    marginTop: 16,
  },
  innerContainer: {flexDirection: 'row', alignItems: 'center'},
});
