import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import useApiHandler from '../../../utilities/useApiHandler';
import {useTranslation} from 'react-i18next';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

type Props = NativeStackScreenProps<AuthStackParamList, 'EmailVerification'>;

// Define validation schema using Yup
const validationSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email.').required('Email is required.'),
});

const Services = new AuthServices();

const EmailVerification: React.FC<Props> = ({navigation}) => {
  const {handleAxiosErrors} = useApiHandler();
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();

  const handleContinue = async () => {
    try {
      setIsLoading(true);

      const resp = await Services.ForgotPassword({
        email: formik.values.email.toLowerCase(),
      });
      if (resp.status == 200) {
        navigation.navigate('OTPVerification', {
          email: formik.values.email.toLowerCase(),
        });
      }
    } catch (error) {
      const err = error as AxiosError;
      const errResp = err.response as Partial<{data: {message: string}}>;
      console.log('AUTH > FORGOT PASSWORD --> ', err.response?.data);

      // INVALID EMAIL PROVIDED
      if (
        err.response?.status === 400 &&
        errResp.data?.message === 'Invalid Email'
      ) {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Please enter a valid email.'),
        });
        return;
      }
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    } finally {
      setIsLoading(false);
    }
  };
  // Initialize useFormik hook
  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validationSchema: validationSchema,
    onSubmit: handleContinue,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.container}
        extraScrollHeight={20}
        enableOnAndroid={true}
        keyboardShouldPersistTaps="handled">
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.innerContainer}>
            <View>
              {/* TITLE */}
              <Text style={styles.title}>
                {t('Enter your details associated with this account')}
              </Text>
              {/* EMAIL */}
              <FormInput
                label={t('Email')}
                value={formik.values.email}
                onChangeText={formik.handleChange('email')}
                onBlur={formik.handleBlur('email')}
                errorMessage={
                  formik.touched.email
                    ? t(formik.errors.email as string)
                    : undefined
                }
                keyboardType="email-address"
              />
            </View>
            {/* CONTINUE */}
            <Button
              title={t('Continue')}
              onPress={formik.handleSubmit}
              disabled={!formik.isValid || isLoading || !formik.dirty}
              loading={isLoading}
              containerStyle={{marginBottom: 10, marginHorizontal: 4}}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default EmailVerification;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    justifyContent: 'space-between',
    paddingBottom: Platform.OS === 'android' ? 40 : 20,
  },
  innerContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 14,
    color: `${theme.lightColors?.secondary}60`,
    fontFamily: Fonts.regular,
    fontWeight: '400',
    paddingBottom: 40,
  },
});
