import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import images from '../../../assets/images';
import {Button} from '../../../components';
import {Button as TextButton} from '@rneui/themed';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {useTranslation} from 'react-i18next';
import {ValidKeyframeProps} from 'react-native-reanimated/lib/typescript/layoutReanimation/animationBuilder/commonTypes';

type Props = NativeStackScreenProps<AuthStackParamList, 'SelectRole'>;

const SelectRole: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();

  const RoleItem = ({
    title,
    paragraph,
    onPressRoleItem,
  }: {
    title: string;
    paragraph: string;
    onPressRoleItem: () => void;
  }) => {
    return (
      <TouchableOpacity style={styles.itemContainer} onPress={onPressRoleItem}>
        <Text style={[styles.title, {paddingTop: 0}]}>{title}</Text>
        <Text style={[styles.paragraph, {marginBottom: 0, paddingTop: 5}]}>
          {paragraph}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <View>
          {/* LOGO */}
          <Image
            resizeMode="contain"
            style={styles.logoContainer}
            source={images.wpf}
          />
          {/* TITLE */}
          <Text style={styles.title}>{t('Join us as')}:</Text>
          <Text style={styles.paragraph}>{t('Please choose your role.')}</Text>
          <RoleItem
            title={t('Client')}
            paragraph={t(
              'Take control of your health and start your journey today!',
            )}
            onPressRoleItem={() =>
              navigation.navigate('SignUp', {isClient: true})
            }
          />
          <RoleItem
            title={t('Coach')}
            paragraph={t('Guide clients towards their health goals!')}
            onPressRoleItem={() =>
              navigation.navigate('SignUp', {isClient: false})
            }
          />
        </View>
        <View style={[styles.footerContainer]}>
          <Text style={styles.footerText}>{t('Already have an account?')}</Text>
          <TextButton
            title={t('Login')}
            type="clear"
            titleStyle={styles.titleStyle}
            onPress={() => navigation.navigate('Login', {isClient: true})}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SelectRole;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: 16,
  },
  logoContainer: {
    width: 110,
    height: 110,
    alignSelf: 'center',
    marginTop: 70,
  },
  title: {
    fontSize: 16,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    paddingTop: 46,
  },
  paragraph: {
    fontFamily: Fonts.regular,
    color: '#2C2C2E80',
    fontSize: 14,
    paddingTop: 2,
    marginBottom: 22,
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  footerText: {
    fontSize: 14,
    fontWeight: '400',
    color: `${theme.lightColors?.black}80`,
    fontFamily: Fonts.regular,
  },
  titleStyle: {
    fontSize: 16,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
  itemContainer: {
    marginBottom: 14,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    paddingHorizontal: 20,
    paddingTop: 17,
    paddingBottom: 26,
    borderRadius: 12,
  },
});
