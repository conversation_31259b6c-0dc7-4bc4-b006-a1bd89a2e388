import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {Button as TextButton} from '@rneui/themed';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomStackParamList} from '../../../navigation/BottomNavigation/BottomNavigation';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import useApiHandler from '../../../utilities/useApiHandler';
type Props = NativeStackScreenProps<BottomStackParamList, 'PreviousPassword'>;

// Define validation schema using Yup
const validationSchema = Yup.object().shape({
  password: Yup.string().required('Current password is required'),
});

const Services = new AuthServices();

interface FormData {
  password: string;
}

const PreviousPassword: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const [hidePassword, setHidePassword] = useState(true);
  const [isLoading, setLoading] = useState(false);

  const handlePreviousPassword = async (formData: FormData) => {
    setLoading(true);
    const payload = {
      oldPassword: formData.password,
      newPassword: formData.password,
    };

    try {
      const resp = await Services.ChangePassword(payload);

      // SUCCESS CHECK OF PASSWORD
      if (resp.status === 200) {
        navigation.navigate('ResetPassword', {
          previousPassword: formik.values.password,
          reset_channel: 'changePassword',
        });
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log(err.response?.data, '-- CLIENT: CHECK CURRENT PASSWORD --');
      // PASSWORD INCORRECT
      if (
        err.response?.data &&
        err.response?.data?.message === 'Old password is incorrect'
      ) {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Invalid password has been entered'),
        });
        setLoading(false);

        return;
      }
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    }
    setLoading(false);
  };

  // Initialize useFormik hook
  const formik = useFormik({
    initialValues: {
      password: '',
    },
    validationSchema: validationSchema,
    onSubmit: handlePreviousPassword,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.container}>
            <View>
              {/* TITLE */}
              <Text style={styles.title}>
                {t('Enter your previous password')}.
              </Text>
              {/* EMAIL */}
              <FormInput
                isPassword
                label={t('Password')}
                value={formik.values.password}
                onChangeText={formik.handleChange('password')}
                errorMessage={
                  formik.touched.password
                    ? t(formik.errors.password as string)
                    : undefined
                }
                secureTextEntry={hidePassword}
                onRightIconPress={() => setHidePassword(!hidePassword)}
              />
              <TextButton
                title={t('Forgot Password?')}
                type="clear"
                titleStyle={styles.forgotPasswordText}
                hitSlop={styles.hitSlop}
                onPress={() => navigation.navigate('EmailVerification')}
              />
            </View>
            {/* CONTINUE */}
            <Button
              title={t('Continue')}
              onPress={formik.handleSubmit}
              loading={isLoading}
              disabled={isLoading || !formik.isValid || !formik.dirty}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default PreviousPassword;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    justifyContent: 'space-between',
    paddingBottom: 80,
    backgroundColor: theme.lightColors?.background,
  },
  title: {
    fontSize: 14,
    color: `${theme.lightColors?.secondary}60`,
    fontFamily: Fonts.regular,
    fontWeight: '400',
    paddingBottom: 40,
  },
  forgotPasswordText: {
    fontSize: 16,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
});
