import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  ScrollView,
  Dimensions,
  SafeAreaView,
  StyleSheet,
  Animated,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {Button} from '../../../components';
import {Fonts, theme} from '../../../utilities/theme';
import {ListItem} from '@rneui/themed';
import {BackArrow, CheckCircle, RightArrow} from '../../../assets/svgIcons';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import SubscriptionServices from '../../../services/SubscriptionServices';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<AuthStackParamList, 'Subscription'>;

const Subscription: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const [isLoading, setLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const windowWidth = Dimensions.get('window').width;
  const scrollViewRef = useRef(null);
  const scrollX = new Animated.Value(0);
  const isProfile = route.params?.isProfile;

  const fetchSubscription = async () => {
    try {
      const resp = await SubscriptionServices.GetSubscriptions();
      if (resp.status == 200) {
        setSubscriptionData(resp.data);
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error fetching subscriptions', err);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchSubscription();
  }, []);

  const handlePaynow = () => {
    if (isProfile) {
      // navigation.pop(2);
      navigation.navigate('AddNewCard');
    } else {
      navigation.navigate('AuthAddNewCard');
    }
  };

  const onForwardArrowPress = () => {
    let newIndex = selectedIndex + 1;
    if (newIndex == 3) {
      return;
    }
    setSelectedIndex(newIndex);
    const scrollPosition = newIndex * windowWidth;
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: scrollPosition,
        animated: true,
      });
    }
  };

  const onBackArrowPress = () => {
    let newIndex = selectedIndex - 1;
    if (newIndex < 0) {
      newIndex = 0; // Ensure newIndex is within bounds
    }
    setSelectedIndex(newIndex);
    const scrollPosition = newIndex * windowWidth;
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: scrollPosition,
        animated: true,
      });
    }
  };
  return (
    <SafeAreaView style={styles.screenWrapper}>
      {/* LOADING */}
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={theme.lightColors?.primary}
          style={styles.activityIndicator}
        />
      ) : null}
      {isProfile ? (
        <View style={{marginTop: 10}} />
      ) : (
        <Text style={styles.title}>{t('Subscription')}</Text>
      )}
      {!isLoading ? (
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={event => {
            const selectedIndex = Math.floor(
              event.nativeEvent.contentOffset.x / Math.floor(windowWidth),
            );
            setSelectedIndex(selectedIndex);
          }}
          onScroll={Animated.event(
            [{nativeEvent: {contentOffset: {x: scrollX}}}],
            {useNativeDriver: false},
          )}
          scrollEventThrottle={16}>
          {subscriptionData?.map((item, index) => {
            const inputRange = [
              (index - 1) * windowWidth,
              index * windowWidth,
              (index + 1) * windowWidth,
            ];
            const scale = scrollX.interpolate({
              inputRange,
              outputRange: [0.3, 1, 0.3],
              extrapolate: 'extend',
            });
            const opacity = scrollX.interpolate({
              inputRange,
              outputRange: [0.3, 1, 0.3],
              extrapolate: 'extend',
            });
            const rotateY = scrollX.interpolate({
              inputRange,
              outputRange: ['-30deg', '0deg', '30deg'],
              extrapolate: 'extend',
            });

            return (
              <View
                key={item.id}
                style={[styles.container, {width: windowWidth}]}>
                <Animated.View
                  style={[
                    styles.innerContainer,
                    {transform: [{scale}], opacity},
                  ]}>
                  {/* SUB-TITLE */}
                  <Text style={styles.packageTypeText}>{item.title}</Text>
                  {/* LARGE TITLE */}
                  <Text style={styles.largeTitle}>
                    {item.price}
                    <Text style={styles.largeTitle2}>{t('Month')}</Text>
                  </Text>
                  {/* LABEL */}
                  <Text style={styles.planIncludeText}>
                    {t('Plan include')}
                  </Text>
                  {/* LIST ITEM */}
                  {item.services.map((_item, index) => (
                    <View key={index}>
                      <ListItem
                        containerStyle={{
                          marginLeft: 12,
                          paddingVertical: 12,
                          backgroundColor: theme.lightColors?.background,
                        }}>
                        <CheckCircle />
                        <ListItem.Content>
                          <ListItem.Title style={styles.listItemTitle}>
                            {_item}
                          </ListItem.Title>
                        </ListItem.Content>
                      </ListItem>
                    </View>
                  ))}
                  {/* VALID TEXT */}
                  <Text style={styles.valditText}>
                    {t('Valid for 1 month')}
                  </Text>
                  {/* CONTINUE BUTTON */}
                  <Button
                    title={t('Pay Now')}
                    containerStyle={{marginHorizontal: 45}}
                    onPress={handlePaynow}
                    rightIcon={<RightArrow stroke={theme.lightColors?.white} />}
                    rightIconContainer={{paddingLeft: 6}}
                  />
                  {/* RIGHT ARROW BUTTON */}
                  {index != 2 ? (
                    <TouchableOpacity
                      style={styles.buttonContainer}
                      onPress={onForwardArrowPress}>
                      <RightArrow stroke={theme.lightColors?.primary} />
                    </TouchableOpacity>
                  ) : null}
                  {/* LEFT ARROW BUTTON */}
                  {index != 0 ? (
                    <TouchableOpacity
                      style={[styles.buttonContainer, {left: -25}]}
                      onPress={onBackArrowPress}>
                      <BackArrow stroke={theme.lightColors?.primary} />
                    </TouchableOpacity>
                  ) : null}
                </Animated.View>
              </View>
            );
          })}
        </ScrollView>
      ) : null}
    </SafeAreaView>
  );
};

export default Subscription;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    alignItems: 'center',
  },
  container: {
    flex: 1,
  },
  innerContainer: {
    marginHorizontal: 40,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: theme.lightColors?.primary,
    // alignItems: 'center',
    paddingTop: 26,
    paddingBottom: 28,
    marginTop: 48,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
    backgroundColor: theme.lightColors?.white,
  },
  title: {
    fontSize: 20,
    lineHeight: 27,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.bold,
    paddingLeft: 24,
    paddingTop: 18,
  },
  packageTypeText: {
    fontSize: 18,
    fontFamily: Fonts.light,
    fontWeight: '400',
    color: theme.lightColors?.secondary,
    textAlign: 'center',
  },
  largeTitle: {
    fontSize: 50,
    color: theme.lightColors?.secondary,
    lineHeight: 60,
    paddingTop: 26,
    fontFamily: Fonts.regular,
    textAlign: 'center',
  },
  largeTitle2: {
    fontSize: 20,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
  },
  planIncludeText: {
    fontSize: 12,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    paddingTop: 30,
    textAlign: 'left',
    paddingLeft: 12,
  },
  listItemTitle: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
  },
  valditText: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    textAlign: 'center',
    paddingVertical: 28,
  },
  buttonContainer: {
    backgroundColor: '#FFF8F6',
    position: 'absolute',
    right: -25,
    top: 280,
    width: 50,
    height: 50,
    borderRadius: 50 / 2,
    zIndex: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  activityIndicator: {
    marginTop: 50,
  },
});
