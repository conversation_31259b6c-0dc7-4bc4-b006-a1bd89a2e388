import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.white,
    paddingHorizontal: 24,
  },
  innerContainer: {
    marginHorizontal: 16,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: theme.lightColors?.primary,
    // alignItems: 'center',
    paddingTop: 26,
    paddingBottom: 28,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    backgroundColor: theme.lightColors?.white,
  },
  packageTypeText: {
    fontSize: 18,
    fontFamily: Fonts.light,
    fontWeight: '400',
    color: theme.lightColors?.secondary,
    textAlign: 'center',
  },
  largeTitle: {
    fontSize: 50,
    color: theme.lightColors?.secondary,
    lineHeight: 60,
    marginTop: 26,
    fontFamily: Fonts.regular,
    textAlign: 'center',
  },
  largeTitle2: {
    fontSize: 20,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
  },
  planIncludeText: {
    fontSize: 12,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    paddingTop: 30,
    textAlign: 'left',
    paddingLeft: 12,
  },
  listItemTitle: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
  },
  valditText: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    textAlign: 'center',
    paddingVertical: 28,
  },
  cancelText: {
    fontFamily: Fonts.semiBold,
    color: theme.darkColors?.error,
    fontSize: 16,
    lineHeight: 21,
  },
  cancelButton: {paddingVertical: 11, paddingHorizontal: 46},
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 30,
    width: '100%',
  },
  buyButtonContainer: {
    paddingVertical: 11,
    paddingHorizontal: 46,
  },
});
