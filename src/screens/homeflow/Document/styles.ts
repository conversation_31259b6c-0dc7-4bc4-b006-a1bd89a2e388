import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  addIconContainer: {position: 'absolute', zIndex: 100, bottom: 36, right: 24},
  addIcon: {
    height: 60,
    width: 60,
  },
  emptyText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 200,
    color: `${theme.lightColors?.black}80`,
  },
  scrollViewContainer: {paddingBottom: 10},
  emptyTextContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
