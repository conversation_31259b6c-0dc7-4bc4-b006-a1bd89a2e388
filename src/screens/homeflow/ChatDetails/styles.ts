import {StyleSheet} from 'react-native';
import {Fonts, sizes, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    // paddingBottom: 20,
    paddingTop: sizes.paddingTop,
  },
  leftTextStyle: {
    color: '#2C2C2E',
    fontFamily: Fonts.medium,
    fontSize: 14,
  },
  rightTextStyle: {
    color: theme.lightColors?.white,
    fontFamily: Fonts.medium,
    fontSize: 14,
  },
  rightWrapperStyle: {
    backgroundColor: theme.lightColors?.primary,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 0,
    marginBottom: 32,
    paddingHorizontal: 10,
    paddingVertical: 3,
  },
  leftWrapperStyle: {
    backgroundColor: theme.lightColors?.grey1,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    borderBottomLeftRadius: 0,
    marginBottom: 32,
    paddingHorizontal: 10,
    paddingVertical: 3,
    marginLeft: -45,
  },
  timeLeftTextStyle: {
    color: '#0000008A',
    textAlign: 'left',
    position: 'absolute',
    top: 15,
  },
  timeRightTextStyle: {
    color: '#0000008A',
    textAlign: 'right',
    position: 'absolute',
    top: 15,
    right: -10,
  },
  containerStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    // paddingTop: 20,
  },
  inputToolbarContainer: {
    backgroundColor: theme.lightColors?.white,
    alignItems: 'center',
    borderWidth: 1,
    borderTopWidth: 1,
    borderColor: '#E8E6EA',
    borderTopColor: '#E8E6EA',
    borderRadius: 10,
    width: '85%',
    flexDirection: 'row',
    paddingLeft: 10,
    paddingTop: 5,
  },
  inputIcons: {
    flexDirection: 'row',
    marginTop: 10,
    width: '100%',
    // zIndex: -200,
  },
  attachmentIcon: {
    position: 'absolute',
    right: 10,
    bottom: -20,
  },
  sendIcon: {
    backgroundColor: theme.lightColors?.black,
    padding: 13,
    borderRadius: 10,
    position: 'absolute',
    right: -60,
    zIndex: 100,
    bottom: 0,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  outerButtonContainer: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 16,
    width: '80%',
    alignSelf: 'center',
  },

  button: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    backgroundColor: 'white',
    borderRadius: 40,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 12,
    justifyContent: 'space-evenly',
    paddingHorizontal: 12,
  },
  innerButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: 'black',
    paddingBottom: 16,
  },
  text: {
    fontSize: 12,
    fontFamily: Fonts.medium,
    color: 'black',
    marginTop: 8,
  },
});
