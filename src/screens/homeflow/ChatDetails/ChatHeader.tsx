import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {Fonts, theme} from '../../../utilities/theme';
import {BackArrow} from '../../../assets/svgIcons';
import {IUser} from '../../../interfaces/IUser.';
import {USER_PLACEHOLDER} from '../../../utilities/constants';

interface Props {
  recipient?: IUser;
}

const ChatHeader: React.FC<Props> = ({recipient}) => {
  const navigation = useNavigation();
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 16,
        marginBottom: 8,
      }}>
      <TouchableOpacity
        style={{width: 30}}
        onPress={() => navigation.goBack()}
        hitSlop={styles.hitSlop}>
        <BackArrow stroke={theme.lightColors?.secondary} />
      </TouchableOpacity>
      <Image
        source={{uri: recipient?.image || recipient?.photo || USER_PLACEHOLDER}}
        style={{height: 40, width: 40, marginRight: 8, borderRadius: 20}}
      />
      <View
        style={{
          flex: 1,
        }}>
        <Text
          style={{
            fontFamily: Fonts.semiBold,
            fontSize: 16,
            color: theme.lightColors?.black,
          }}>
          {recipient?.name}
        </Text>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <View
            style={{
              width: 4,
              height: 4,
              borderRadius: 8 / 2,
              backgroundColor: theme.lightColors?.success,
              marginRight: 2,
            }}
          />
          <Text
            style={{
              fontFamily: Fonts.regular,
              fontSize: 10,
              color: '#ACACAC',
            }}>
            Online
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ChatHeader;

const styles = StyleSheet.create({
  hitSlop: {
    left: 15,
    right: 15,
    bottom: 15,
    top: 15,
  },
});
