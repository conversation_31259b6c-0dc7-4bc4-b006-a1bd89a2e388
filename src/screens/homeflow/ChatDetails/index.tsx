import {ActivityIndicator, Image, SafeAreaView, Text, View} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  Bubble,
  GiftedChat,
  IMessage,
  InputToolbar,
  Send,
  Time,
} from 'react-native-gifted-chat';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {TouchableOpacity} from 'react-native';
import ChatHeader from './ChatHeader';
import PNGIcons from '../../../assets/pngIcons';
import {styles} from './styles';
import ChatServices from '../../../services/ChatServices';
import {useAppDispatch, useAppSelector} from '../../../store';
import {AxiosError} from 'axios';
import moment from 'moment';
import {theme} from '../../../utilities/theme';
import EmojiSelector from 'react-native-emoji-selector';
import ImageCropPicker from 'react-native-image-crop-picker';
import Modal from 'react-native-modal';
import {useTranslation} from 'react-i18next';
import {
  appendMessages,
  setChatMessages,
  updateChat,
  updateUnreadCounter,
} from '../../../store/chatsSlice';
// import {getSocket} from '../../../utilities/socket';

interface MessageData {
  _id: string;
  chatId: string;
  content: string;
  createdAt: string;
  read: boolean;
  sender: string;
  updatedAt: string;
}

type Props = NativeStackScreenProps<HomeStackParamList, 'ChatDetails'>;
const ChatDetails: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();

  // const [messages, setMessages] = useState<IMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEmojiModalVisible, setIsEmojiModalVisible] = useState(false);
  const [text, setText] = useState('');
  const chatId = route.params.chatId;
  // const {fetchChats} = useChats();

  const [isModalVisible, setModalVisible] = useState(false);
  const [imageLocalPath, setImageLocalPath] = useState<string>('');
  const [error, setError] = useState('');

  const recipient = route.params.recipient;
  const dispatch = useAppDispatch();

  const user = useAppSelector(state => state.user.user);
  const {chatMessages} = useAppSelector(state => state.chats);
  // const socket = getSocket();

  const fetchMessages = async () => {
    try {
      if (!chatId) {
        return;
      }

      // IF CHAT ID EXISTS, FETCH MESSAGES
      const resp = await ChatServices.GetMessages(chatId);
      if (resp.status == 200) {
        const formattedMessages = resp.data.results.map(
          (chatItem: MessageData) => ({
            _id: chatItem._id,
            text: chatItem.content,
            createdAt: moment(chatItem.createdAt),
            read: chatItem.read,
            user: {
              _id: chatItem.sender,
            },
          }),
        );
        dispatch(setChatMessages(formattedMessages));
        // setMessages(formattedMessages);
      }
    } catch (error) {
      const err = error as AxiosError;
      setError(err.message);
      console.log('Error fetching chat history', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (chatId) {
      fetchMessages();
    }
  }, [chatId]);

  const handlePickImage = () => {
    ImageCropPicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
      maxFiles: 1,
      mediaType: 'photo',
    }).then(image => {
      setModalVisible(false);
      setImageLocalPath(image.path);
    });
  };

  const handleCameraImage = () => {
    ImageCropPicker.openCamera({
      width: 300,
      height: 400,
      cropping: true,
      mediaType: 'photo',
    }).then(image => {
      console.log(image);
      setModalVisible(false);
      setImageLocalPath(image.path);
    });
  };

  // UPDATE MESSAGE - MARK AS READ NEW MESSAGE SENT BY OTHERS
  async function updateMessage() {
    try {
      const unReadMsgs = chatMessages.filter(
        item => item.read === false && item.user._id !== user._id,
      );

      if (unReadMsgs.length > 0) {
        const promises = unReadMsgs.map(item =>
          ChatServices.UpdateMessage(item?._id.toString(), {read: true}),
        );
        await Promise.all(promises);
        unReadMsgs.forEach(item => {
          dispatch(updateChat({chat: {...item, unReadMsgCount: 0}}));
        });
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('CHAT DETAILS > UPDATE MSG -> ', err.response?.data);
    }
  }

  const onSend = useCallback(
    async (messages: IMessage[]) => {
      if (!recipient) return;

      dispatch(appendMessages(messages));
      const resp = await ChatServices.SendMessage({
        message: messages[0].text,
        receiverId: recipient._id,
      });
      // if (resp.status === 200) {
      //   fetchChats();
      // }
    },
    [recipient],
  );

  const onEmojiSelected = (emoji: string) => {
    setText(text + emoji);
    setIsEmojiModalVisible(false);
  };

  useEffect(() => {
    if (chatMessages) {
      updateMessage();
    }
  }, [chatMessages]);

  return (
    <View style={styles.container}>
      <SafeAreaView style={{flex: 1, paddingBottom: 20}}>
        <ChatHeader recipient={recipient} />
        {isLoading ? (
          <ActivityIndicator
            color={theme.lightColors?.primary}
            size={'large'}
            style={{marginTop: 100}}
          />
        ) : (
          <GiftedChat
            messages={chatMessages}
            onSend={messages => onSend(messages)}
            user={{
              _id: user._id,
            }}
            alwaysShowSend
            showUserAvatar={false}
            showAvatarForEveryMessage={false}
            placeholder={t('Your message')}
            renderAvatarOnTop={false}
            listViewProps={{showsVerticalScrollIndicator: false}}
            renderBubble={props => {
              return (
                <Bubble
                  {...props}
                  textStyle={{
                    left: styles.leftTextStyle,
                    right: styles.rightTextStyle,
                  }}
                  wrapperStyle={{
                    right: styles.rightWrapperStyle,
                    left: styles.leftWrapperStyle,
                  }}
                />
              );
            }}
            renderSend={props => (
              <Send
                {...props}
                disabled={!props.text}
                containerStyle={styles.containerStyle}>
                <TouchableOpacity
                  disabled={!props.text}
                  onPress={() => {
                    const trimmedText = props.text?.trim() ?? '';
                    if (props.onSend) {
                      props.onSend({text: trimmedText}, true);
                    }
                  }}
                  hitSlop={4}
                  style={styles.sendIcon}>
                  <Image
                    source={PNGIcons.ChatSendIcon}
                    style={{
                      height: 24,
                      width: 24,
                      tintColor: theme.lightColors.white,
                    }}
                  />
                </TouchableOpacity>
              </Send>
            )}
            renderInputToolbar={props => (
              <InputToolbar
                {...props}
                containerStyle={styles.inputToolbarContainer}
                primaryStyle={{}}
                // textInputStyle={{color: 'black'}}
                renderAccessory={() => (
                  <View style={styles.inputIcons}>
                    <TouchableOpacity
                      style={styles.attachmentIcon}
                      hitSlop={10}
                      onPress={() => setModalVisible(true)}>
                      <Image
                        source={PNGIcons.AttachmentIcon}
                        style={{width: 20, height: 20}}
                      />
                    </TouchableOpacity>
                  </View>
                )}
              />
            )}
            renderActions={() => (
              <TouchableOpacity
                style={{marginBottom: 15}}
                onPress={() => setIsEmojiModalVisible(!isEmojiModalVisible)}>
                <Image
                  source={PNGIcons.EmojiIcon}
                  style={{width: 20, height: 20}}
                />
              </TouchableOpacity>
            )}
            renderTime={props => (
              <Time
                {...props}
                timeTextStyle={{
                  left: styles.timeLeftTextStyle,
                  right: styles.timeRightTextStyle,
                }}
              />
            )}
            text={text}
            onInputTextChanged={setText}
          />
        )}
      </SafeAreaView>
      {isEmojiModalVisible && (
        <EmojiSelector onEmojiSelected={emoji => onEmojiSelected(emoji)} />
      )}

      <Modal
        isVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}>
        <View style={styles.outerButtonContainer}>
          <Text style={[styles.title, {alignSelf: 'center'}]}>
            {t('Upload')}
          </Text>
          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={styles.innerButtonContainer}
              onPress={handleCameraImage}>
              <Image source={PNGIcons.Camera} style={{width: 24, height: 24}} />
              <Text style={styles.text}>{t('Camera')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.innerButtonContainer}
              onPress={handlePickImage}>
              <Image
                source={PNGIcons.Gallery}
                style={{width: 24, height: 24}}
              />
              <Text style={styles.text}>{t('Gallery')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ChatDetails;
