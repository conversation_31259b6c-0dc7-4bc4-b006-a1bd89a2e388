import {Pressable, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from 'react-native-popup-menu';
import {ChevronDown} from '../../../assets/svgIcons';
import {Fonts, theme} from '../../../utilities/theme';
import {Button} from '../../../components';
import {useTranslation} from 'react-i18next';

interface Props {
  opened: boolean;
  setOpened: (opened: boolean) => void;
  onPress: (label: string) => void;
}

const PopUpMenu: React.FC<Props> = ({opened, setOpened, onPress}) => {
  const {t} = useTranslation();

  const MENULIST = [
    {id: 1, label: 'Name'},
    {id: 2, label: 'New'},
    {id: 3, label: 'Old'},
  ];

  return (
    <View>
      <Menu opened={opened} onBackdropPress={() => setOpened(false)}>
        <MenuTrigger>
          <TouchableOpacity>
            <Button
              title={t('Sort by')}
              titleStyle={styles.title}
              containerStyle={styles.containerStyle}
              rightIcon={<ChevronDown />}
              onPress={() => setOpened(true)}
            />
          </TouchableOpacity>
        </MenuTrigger>
        <MenuOptions
          customStyles={{
            optionsContainer: [styles.customStyles],
          }}>
          {MENULIST.map((item, index) => {
            return (
              <MenuOption
                key={item.id}
                onSelect={() => {
                  onPress(item.label);
                  setOpened(false);
                }}>
                <View style={styles.menuActions}>
                  <Text style={[styles.menuLabel]}>{t(item.label)}</Text>
                </View>
                {index !== item.label.length - 1 && (
                  <View style={[styles.menuDivider]} />
                )}
              </MenuOption>
            );
          })}
        </MenuOptions>
      </Menu>
    </View>
  );
};

export default PopUpMenu;

const styles = StyleSheet.create({
  customStyles: {
    backgroundColor: theme.lightColors?.background,
    borderRadius: 8,
    width: 100,
    justifyContent: 'center',
    marginTop: 20,
    marginLeft: -20,
  },
  menuActions: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    alignSelf: 'center',
  },
  menuDivider: {
    height: 1,
    backgroundColor: theme.lightColors?.grey2,
    width: '100%',
    alignSelf: 'center',
    marginTop: 6,
  },
  menuLabel: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
  },
  containerStyle: {
    width: 60,
    height: 20,
    paddingVertical: 0,
    backgroundColor: theme.lightColors?.grey3,
  },
  title: {
    fontSize: 12,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
    paddingRight: 6,
  },
});
