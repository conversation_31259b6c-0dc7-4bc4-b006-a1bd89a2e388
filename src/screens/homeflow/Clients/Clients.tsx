import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Header, SearchBar} from '../../../components';
import {Plus} from '../../../assets/svgIcons';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomStackParamList} from '../../../navigation/BottomNavigation/BottomNavigation';
import PopUpMenu from './PopUpMenu';
import {useAppSelector} from '../../../store';
import {styles} from './styles';
import {useDebounce} from 'use-debounce';
import images from '../../../assets/images';
import ClientListItem from './ClientListItem';
import {useTranslation} from 'react-i18next';
import useClients from '../../../hooks/models/useClients';
import {theme} from '../../../utilities/theme';

type Props = NativeStackScreenProps<BottomStackParamList, 'Clients'>;

const Clients: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const {fetchClientsForCoach, clients, count, isLoading} = useClients();
  const [searchString, setSearchString] = useState('');
  const [opened, setOpened] = useState(false);
  const [sortKey, setsortKey] = useState('');
  const [value] = useDebounce(searchString, 800);
  const currentUser = useAppSelector(state => state.user.user);

  useEffect(() => {
    fetchClientsForCoach(sortKey, value);
  }, [value, sortKey]);

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <Header
          showDeleteButton
          onBellPress={() => navigation.navigate('Notifications')}
          onDeletePress={() => navigation.navigate('Archive')}
          onAvatarPress={() => navigation.navigate('Profile')}
        />
        <SearchBar
          value={searchString}
          onChangeText={txt => setSearchString(txt)}
        />

        {isLoading ? (
          <ActivityIndicator
            color={theme.lightColors?.primary}
            size={'large'}
            style={styles.activityIndicator}
          />
        ) : (
          <>
            {count ? (
              <View style={styles.rowContainer}>
                <Text style={styles.clientText}>
                  {t('Total Clients')}:{' '}
                  <Text style={styles.clientText2}>{count}</Text>
                </Text>
                <PopUpMenu
                  opened={opened}
                  setOpened={setOpened}
                  onPress={label => setsortKey(label)}
                />
              </View>
            ) : null}

            <ScrollView
              contentContainerStyle={{paddingBottom: 2}}
              showsVerticalScrollIndicator={false}>
              {count ? (
                clients.map(item => {
                  return (
                    <ClientListItem
                      key={item._id}
                      client={item}
                      onClientPress={() =>
                        navigation.navigate('ClientDetail', {details: item})
                      }
                      onChatPress={() => {
                        const chat = currentUser.chats?.find(c =>
                          c.users.includes(item._id),
                        );
                        navigation.navigate('ChatDetails', {
                          recipient: item,
                          chatId: chat?._id,
                        });
                      }}
                    />
                  );
                })
              ) : (
                <View style={styles.emptyContainer}>
                  <Image
                    source={images.noClients}
                    style={styles.noClientsImage}
                    resizeMode="contain"
                  />
                  <Text style={styles.noClientsHeading}>
                    {t('No clients found')}
                  </Text>
                  <Text style={styles.noClientsText}>
                    {t(
                      'Please invite clients by providing your referral code to see them here',
                    )}
                  </Text>
                </View>
              )}
            </ScrollView>
            <TouchableOpacity
              style={styles.buttonContainer}
              onPress={() => navigation.navigate('AddClient')}>
              <Plus />
            </TouchableOpacity>
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

export default Clients;
