import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import images from '../../../assets/images';
import {TermsIcon} from '../../../assets/svgIcons';
import {useTranslation} from 'react-i18next';

const TermsAndCondition = () => {
  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.logo}>
            {/* LOGO */}
            <TermsIcon width={100} height={100} />
          </View>
          {/* PARAGRPH */}
          <Text style={styles.paragraph}>
            {t(
              "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s the industry's standard dummy text ever since the 1500s, when an unknown printer. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the",
            )}
          </Text>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default TermsAndCondition;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingBottom: 16,
    paddingHorizontal: 24,
  },
  paragraph: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    paddingTop: 20,
  },
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  containerStyle: {
    marginLeft: 0,
    marginTop: 56,
    paddingVertical: 0,
    paddingLeft: 0,
  },
  textStyle: {
    fontSize: 12,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.light,
    marginLeft: 10,
    fontWeight: '400',
  },
  logo: {alignItems: 'center'},
});
