import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import PdfFileContainer from '../../../components/PdfFileContainer/PdfFileContainer';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {IDocument} from '../../../interfaces/IDocument';
import {useTranslation} from 'react-i18next';
import {Button} from '../../../components';

type Props = NativeStackScreenProps<HomeStackParamList, 'DocumentDetail'>;

const DocumentDetail: React.FC<Props> = ({navigation, route}) => {
  const document = route.params?.document as IDocument;
  const {t} = useTranslation();
  const handleDeleteDocument = () => {
    //TODO: Delete document
    console.log('Delete document pressed in Document Details');
  };
  const handleEditDocument = () => {
    //TODO: Edit document
    console.log('Edit document pressed in Document Details');
  };

  return (
    <View style={styles.container}>
      <View>
        <Text style={styles.heading}>{t('Name')}</Text>
        <Text style={styles.title}>{document?.title}</Text>
        <Text style={[styles.heading, {marginTop: 14}]}>
          {t('Description')}
        </Text>
        <Text style={styles.description}>{document?.description}</Text>

        <PdfFileContainer
          details
          fileName={document.title}
          fileUrl={document.docUrl}
        />
      </View>
      <View style={styles.buttonContainer}>
        <View style={styles.button}>
          <Button
            title={t('Delete')}
            containerStyle={styles.deleteButtonConatiner}
            titleStyle={styles.deleteButton}
            onPress={handleDeleteDocument}
          />
        </View>
        <View style={styles.button}>
          <Button title={t('Edit')} onPress={() => handleEditDocument()} />
        </View>
      </View>
    </View>
  );
};

export default DocumentDetail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingBottom: 24,
  },
  heading: {
    fontSize: 12,
    fontFamily: Fonts.regular,
    marginTop: 35,
    color: '#A0A0A0',
  },
  description: {
    fontFamily: Fonts.medium,
    marginTop: 8,
    marginBottom: 24,
    color: 'black',
  },
  title: {
    marginTop: 8,
    fontFamily: Fonts.semiBold,
    color: 'black',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  button: {
    flex: 1,
  },
  deleteButtonConatiner: {
    backgroundColor: theme.lightColors?.background,
  },
  deleteButton: {
    color: theme.lightColors?.black,
  },
});
