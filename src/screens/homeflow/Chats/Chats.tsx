import {Image, SafeAreaView, ScrollView, StyleSheet, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {Header, SearchBar} from '../../../components';
import ChatItem from './ChatItem';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomStackParamList} from '../../../navigation/BottomNavigation/BottomNavigation';
import {useAppSelector} from '../../../store';
import {Text} from 'react-native';
import images from '../../../assets/images';
import {IQueryResponse} from '../../../interfaces/QueryResponse';
import {IChat} from '../../../interfaces/IChat';
import {useTranslation} from 'react-i18next';
import useChats from '../../../hooks/models/useChats';
import {useDebounce} from 'use-debounce';

type Props = NativeStackScreenProps<BottomStackParamList, 'Chats'>;

const Chats: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();

  const [searchString, setSearchString] = useState('');
  const chats = useAppSelector(state => state.chats.chats);
  const [filteredChats, setFilteredChats] =
    useState<IQueryResponse<IChat>>(chats);
  // const [isLoading, setIsLoading] = useState(false);
  const currentUser = useAppSelector(state => state.user.user);
  const [value] = useDebounce(searchString, 800);
  const {isLoading} = useChats();
  const [doNotDisturb, setDoNotDisturb] = useState(false);

  const filterChats = () => {
    if (!value) {
      return chats;
    }

    const remainingChats = chats?.results.filter(chat => {
      const found = chat.users.find(
        user => user.name.includes(value) && user._id !== currentUser._id,
      );
      return !!found;
    });

    return remainingChats
      ? {count: remainingChats.length, results: remainingChats}
      : {count: 0, results: []};
  };

  useEffect(() => {
    const updatedFilteredChats = filterChats();
    setFilteredChats(updatedFilteredChats);
  }, [value, chats, currentUser._id]);

  const handleSearch = (txt: string) => {
    setSearchString(txt);
  };

  const handleUnReadMsg = (chat: IChat) => {
    if (chat.unReadMsgCount && chat.lastMessage.sender !== currentUser._id) {
      return chat.unReadMsgCount;
    } else return 0;
  };
  const handleDoNotDisturb = () => {
    setDoNotDisturb(!doNotDisturb);
    // TODO: update do not disturb implementation
    console.log('do not disturb need to be implemented, State:', doNotDisturb);
  };

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        {/* HEADER */}
        <Header
          doNotDisturb={doNotDisturb}
          onBellPress={() => navigation.navigate('Notifications')}
          onDoNotDisturbPress={handleDoNotDisturb}
          onAvatarPress={() => navigation.navigate('Profile')}
          showDoNotDisturbButton={currentUser.userType === 'coach'}
          // showDeleteButton
        />
        {/* SEARCH BAR */}
        {currentUser.userType === 'coach' ? (
          <SearchBar value={searchString} onChangeText={handleSearch} />
        ) : null}
        {currentUser.userType === 'client' ? (
          <View style={styles.headingText}>
            <Text style={styles.myCoachText}>{t('My Coach')}</Text>
            <Text style={styles.contactCoachText}>
              {t('Contact Your Coach Anytime!')}
            </Text>
          </View>
        ) : null}
        {/* DATA STATE */}
        {!isLoading && filteredChats?.count ? (
          <ScrollView
            contentContainerStyle={{paddingBottom: 2}}
            showsVerticalScrollIndicator={false}>
            {/* CHATS LIST USER */}
            {filteredChats?.count
              ? filteredChats.results.map(item => {
                  const recipient = item.users.find(
                    u => u._id !== currentUser._id,
                  );

                  return (
                    <ChatItem
                      key={item._id}
                      chatItem={item}
                      onPress={() => {
                        navigation.navigate('ChatDetails', {
                          chatId: item._id,
                          recipient,
                        });
                      }}
                      unReadMsgCount={handleUnReadMsg(item)}
                    />
                  );
                })
              : null}
          </ScrollView>
        ) : null}

        {/* EMPTY STATE */}
        {!isLoading && !filteredChats?.count ? (
          <View style={{alignItems: 'center', marginTop: 40}}>
            <Image
              source={images.noChat}
              style={styles.noChatImage}
              resizeMode="contain"
            />
            <Text style={styles.noChatHeading}>{t('No new messages')}</Text>
            <Text style={styles.notChatText}>{t('Your inbox is empty')}</Text>
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
};

export default Chats;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    backgroundColor: theme.lightColors?.background,
  },
  emptyState: {
    color: theme.lightColors?.black,
    textAlign: 'center',
    marginVertical: 24,
  },
  noChatImage: {
    height: 120,
    width: 150,
  },
  noChatHeading: {
    color: '#00000080',
    marginTop: 16,
    fontWeight: '600',
    fontSize: 16,
    fontFamily: Fonts.bold,
  },
  notChatText: {
    fontSize: 12,
    color: '#00000080',
    marginTop: 8,
  },
  myCoachText: {
    fontSize: 16,
    fontFamily: Fonts.bold,
    color: theme.lightColors?.black,
    marginTop: 24,
  },
  contactCoachText: {
    fontSize: 14,
    fontFamily: Fonts.regular,
    color: theme.lightColors?.black,
    marginTop: 4,
  },
  headingText: {
    paddingVertical: 20,
  },
});
