import {<PERSON>rollView, Text, View} from 'react-native';
import React, {useState} from 'react';
import {theme} from '../../../utilities/theme';
import WeightTrackerGraph from '../../../components/WeightTrackerGraph/WeightTrackerGraph';
import WeightRecord from '../../../components/WeightRecord/WeightRecord';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import Modal from 'react-native-modal';
import ImagePicker from 'react-native-image-crop-picker';
import {TouchableOpacity} from 'react-native';
import {Image} from 'react-native';
import {Button, FormInput} from '../../../components';
import PNGIcons from '../../../assets/pngIcons';
import {styles} from './styles';
import moment from 'moment';
import {useAppSelector} from '../../../store';
import {useTranslation} from 'react-i18next';
import {IWeight} from '../../../interfaces/IWeight';
import WeightForm from '../../clientflow/ClientHome/WeightForm';

type Props = NativeStackScreenProps<ClientStackParamList, 'WeightProgress'>;
const WeightProgress: React.FC<Props> = ({route}) => {
  const [isModal, setIsModal] = useState(false);
  const [unit, setUnit] = useState<'kg' | 'lb'>('kg');
  const [image, setImage] = useState('');
  const [clickedWeight, setClickedWeight] = useState<IWeight>();

  const weights = useAppSelector(state => state.statistics.weights);
  const {t} = useTranslation();

  const handleUpload = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: 'photo',
      });
      setImage(image.path);
    } catch (error) {
      console.log('Error selecting image', error);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        {/* LINE GRAPH */}
        {/* WEIGHT TRACKING GRAPH */}
        <WeightTrackerGraph
          client={true}
          weights={weights}
          onPress={() => {
            setClickedWeight(undefined);
            setIsModal(true);
          }}
          onPressWeight={weight => {
            setClickedWeight(weight);
            setIsModal(true);
          }}
        />

        {/* PROGRESS RECORD WITH PICTURES */}
        {weights[0]?.photos[1] &&
        weights[0]?.photos[0] &&
        weights[0]?.photos[2] ? (
          <>
            <View style={styles.divider} />
            <Text style={styles.recordText}>{t('Progress Record')}</Text>
            {weights.map(item => {
              if (
                item?.photos[1]?.url &&
                item?.photos[0]?.url &&
                item?.photos[2]?.url
              ) {
                return (
                  <WeightRecord
                    date={moment(+item.createdAt).format('DD/MM/YY')}
                    weight={+item.value}
                    front={item.photos[1].url}
                    side={item.photos[0].url}
                    back={item.photos[2].url}
                  />
                );
              }
            })}
          </>
        ) : null}
      </ScrollView>
      {/* <Modal
        isVisible={isModal}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setIsModal(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={styles.modalStyle}>
        <View style={styles.modalContainer}>
          <View>
            <Text style={styles.modalHeading}>{t('Add weight')}</Text>
            <View style={styles.divider} />
            <FormInput label={t('Enter you weight')} />
            <View style={styles.units}>
              <TouchableOpacity
                style={[
                  styles.unitContainer,
                  {
                    backgroundColor:
                      unit === 'kg'
                        ? theme.lightColors?.primary
                        : theme.lightColors?.grey1,
                  },
                ]}
                onPress={() => setUnit('kg')}>
                <Text
                  style={{
                    color: unit === 'kg' ? theme.lightColors?.white : '#969696',
                  }}>
                  kg
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.unitContainer,
                  {
                    backgroundColor:
                      unit === 'lb'
                        ? theme.lightColors?.primary
                        : theme.lightColors?.grey1,
                  },
                ]}
                onPress={() => setUnit('lb')}>
                <Text
                  style={{
                    color: unit === 'lb' ? theme.lightColors?.white : '#969696',
                  }}>
                  lb
                </Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.uploadText}>{t('Upload image')}</Text>

            <TouchableOpacity
              style={styles.uploadContainer}
              onPress={handleUpload}>
              {image ? (
                <Image source={{uri: image}} style={styles.selectedImage} />
              ) : (
                <Image
                  source={PNGIcons.UploadImage}
                  style={styles.uploadIcon}
                />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.deleteFoodContainer}
              onPress={() => setIsModal(false)}>
              <Text style={styles.deleteFood}>{t('Cancel')}</Text>
            </TouchableOpacity>
            <Button
              title={t('Add')}
              containerStyle={styles.saveButton}
              onPress={() => setIsModal(false)}
            />
          </View>
        </View>
      </Modal> */}
      {/* ADD WEIGHT MODAL */}
      <Modal
        isVisible={isModal}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setIsModal(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{margin: 0, justifyContent: 'flex-end'}}>
        <View style={styles.modalContainer}>
          <WeightForm
            onCancel={() => setIsModal(false)}
            onSubmitted={() => setIsModal(false)}
            weight={clickedWeight}
          />
        </View>
      </Modal>
    </View>
  );
};

export default WeightProgress;
