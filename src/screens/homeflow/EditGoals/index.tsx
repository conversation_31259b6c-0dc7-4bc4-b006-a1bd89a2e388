import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {useFormik} from 'formik';
import * as yup from 'yup';
import ClientServices, {IClientGoal} from '../../../services/ClientServices';
import Toast from 'react-native-toast-message';
import {calculateCalories} from '../../../utilities/app.utils';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<HomeStackParamList, 'EditGoals'>;

const EditGoals: React.FC<Props> = ({navigation, route}) => {
  const [loading, setLoading] = useState(false);
  const {t} = useTranslation();

  const validationSchema = yup.object().shape({
    calories: yup.string().required(t('Calories is required')),
    proteins: yup.string().required(t('Proteins is required')),
    proteinsPercentage: yup.string().required(t('Proteins % is required')),
    carbs: yup.string().required(t('Carbs is required')),
    carbsPercentage: yup.string().required(t('Carbs % is required')),
    fat: yup.string().required(t('Fat is required')),
    fatPercentage: yup.string().required(t('Fat % is required')),
  });

  const currentGoal = useMemo(() => route.params?.currentGoal, [route.params]);

  async function updateClientGoal(formData: IClientGoal) {
    // PAYLOAD
    const goalPayload = {
      calories: formData.calories,
      proteins: formData.proteins,
      proteinsPercentage: formData.proteinsPercentage,
      carbs: formData.carbs,
      carbsPercentage: formData.carbsPercentage,
      fat: formData.fat,
      fatPercentage: formData.fatPercentage,
      client: route.params?.clientId,
    } as IClientGoal;

    // UPDATE EXSTING GOAL
    if (currentGoal) {
      try {
        setLoading(true);
        const resp = await ClientServices.UpdateClientGoal(
          currentGoal?._id,
          goalPayload,
        );

        // SUCCESS
        if (resp.status === 200) {
          setLoading(false);
          Toast.show({
            type: 'success',
            text1: t('Success'),
            text2: t('Goal updated successfully.'),
          });
          navigation.goBack();
        }
      } catch (error) {
        console.log('----------- Set Goal error:', error);
        setLoading(false);
        Toast.show({
          type: 'error',
          text1: t('ERROR'),
          text2: t("Couldn't update the goal."),
        });
      }
      return;
    }

    // CREATE NEW GOAL FOR CLIENT
    if (!currentGoal) {
      try {
        setLoading(true);
        const resp = await ClientServices.SetClientGoal(goalPayload);

        // SUCCESS
        if (resp.status === 201) {
          setLoading(false);
          formik.resetForm();
          Toast.show({
            type: 'success',
            text1: t('Success'),
            text2: t('Goal created successfully.'),
          });
          navigation.goBack();
        }
      } catch (error) {
        console.log('----------- Set Goal error:', error);
        setLoading(false);
        Toast.show({
          type: 'error',
          text1: t('ERROR'),
          text2: t("Couldn't update the review."),
        });
      }
    }
  }

  const formik = useFormik({
    initialValues: {
      calories: currentGoal?.calories || 0,
      proteins: currentGoal?.proteins || 0,
      proteinsPercentage: currentGoal?.proteinsPercentage || 0,
      carbs: currentGoal?.carbs || 0,
      carbsPercentage: currentGoal?.carbsPercentage || 0,
      fat: currentGoal?.fat || 0,
      fatPercentage: currentGoal?.fatPercentage || 0,
      client: route.params?.clientId,
    },
    onSubmit: updateClientGoal,
    validationSchema: validationSchema,
    enableReinitialize: true,
  });

  const {proteins, carbs, fat} = formik.values;

  useEffect(() => {
    let calInfo = calculateCalories({proteins, carbs, fat});
    formik.setFieldValue('calories', calInfo.sum);
    formik.setFieldValue('proteinsPercentage', calInfo.proteinsPercentage || 0);
    formik.setFieldValue('carbsPercentage', calInfo.carbsPercentage || 0);
    formik.setFieldValue('fatPercentage', calInfo.fatPercentage || 0);
  }, [carbs, fat, proteins]);

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View>
            <Text style={styles.heading}>
              {t('Set your client’s daily goals.')}
            </Text>

            {/* CALORIES */}
            <FormInput
              label={t('Calories Goal')}
              unitText={t('cal.')}
              value={formik.values.calories.toString()}
              onChangeText={formik.handleChange('calories')}
              onBlur={formik.handleBlur('calories')}
              errorMessage={
                formik.touched.calories
                  ? t(formik.errors.calories as string)
                  : undefined
              }
              disabled
            />

            {/* PROTEINS */}
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  label={t('Proteins Goal')}
                  unitText="g"
                  value={formik.values.proteins.toString()}
                  onChangeText={formik.handleChange('proteins')}
                  onBlur={formik.handleBlur('proteins')}
                  keyboardType="numeric"
                  errorMessage={
                    formik.touched.proteins
                      ? t(formik.errors.proteins as string)
                      : undefined
                  }
                />
              </View>

              {/* PROTEINS PERCENTAGE */}
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  label=" "
                  unitText="%"
                  value={formik.values.proteinsPercentage.toString()}
                  onChangeText={formik.handleChange('proteinsPercentage')}
                  onBlur={formik.handleBlur('proteinsPercentage')}
                  errorMessage={
                    formik.touched.proteinsPercentage
                      ? t(formik.errors.proteinsPercentage as string)
                      : undefined
                  }
                  disabled
                />
              </View>
            </View>

            {/* CARBS */}
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  label={t('Carbs Goal')}
                  unitText="g"
                  value={formik.values.carbs.toString()}
                  onChangeText={formik.handleChange('carbs')}
                  onBlur={formik.handleBlur('carbs')}
                  keyboardType="numeric"
                  errorMessage={
                    formik.touched.carbs
                      ? t(formik.errors.carbs as string)
                      : undefined
                  }
                />
              </View>

              {/* CARBS PERCENTAGE */}
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  label=" "
                  unitText="%"
                  value={formik.values.carbsPercentage.toString()}
                  onChangeText={formik.handleChange('carbsPercentage')}
                  onBlur={formik.handleBlur('carbsPercentage')}
                  errorMessage={
                    formik.touched.carbsPercentage
                      ? t(formik.errors.carbsPercentage as string)
                      : undefined
                  }
                  disabled
                />
              </View>
            </View>

            {/* FATS */}
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  label={t('Fat Goal')}
                  unitText="g"
                  value={formik.values.fat.toString()}
                  onChangeText={formik.handleChange('fat')}
                  onBlur={formik.handleBlur('fat')}
                  keyboardType="numeric"
                  errorMessage={
                    formik.touched.fat
                      ? t(formik.errors.fat as string)
                      : undefined
                  }
                />
              </View>

              {/* FATS PERCENTAGE */}
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  label=" "
                  unitText="%"
                  value={formik.values.fatPercentage.toString()}
                  onChangeText={formik.handleChange('fatPercentage')}
                  onBlur={formik.handleBlur('fatPercentage')}
                  errorMessage={
                    formik.touched.fatPercentage
                      ? t(formik.errors.fatPercentage as string)
                      : undefined
                  }
                  disabled
                />
              </View>
            </View>
          </View>

          {/* SAVE BUTTON */}
          <Button
            title={t('Save Changes')}
            containerStyle={styles.buttonContainer}
            onPress={formik.handleSubmit}
            loading={loading}
            disabled={loading || !formik.isValid}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default EditGoals;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  heading: {
    marginTop: 35,
    marginBottom: 24,
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    letterSpacing: 0.7,
    color: theme.lightColors?.secondary,
  },
  goalInputs: {flexDirection: 'row', alignItems: 'center'},
  buttonContainer: {
    flex: 1,
    marginTop: 120,
  },
});
