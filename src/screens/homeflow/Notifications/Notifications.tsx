import {SafeAreaView, <PERSON>rollView, StyleSheet, View} from 'react-native';
import React from 'react';
import NotificationItem from './NotificationItem';
import {theme} from '../../../utilities/theme';

const Notifications = () => {
  const notifications = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}];
  return (
    <SafeAreaView style={styles.outerContainer}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.container}>
          {notifications.map((item, index) => {
            return (
              <NotificationItem
                key={index}
                isActive={index == 0 ? true : false}
              />
            );
          })}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Notifications;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingVertical: 16,
  },
  outerContainer: {flex: 1, backgroundColor: theme.lightColors?.background},
});
