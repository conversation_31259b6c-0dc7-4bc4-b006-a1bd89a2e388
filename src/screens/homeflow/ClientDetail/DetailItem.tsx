import React from 'react';
import {styles} from './styles';
import {t} from 'i18next';
import {StyleSheet, Text, TextStyle, View, ViewStyle} from 'react-native';
import {theme} from '../../../utilities/theme';

//  props
interface DetailItemProps {
  data: string | number;
  title: string;
  containerStyle?: ViewStyle;
  headingStyle?: TextStyle;
  descriptionStyle?: TextStyle;
}
const DetailItem = ({
  data,
  title,
  containerStyle,
  headingStyle,
  descriptionStyle,
}: DetailItemProps) => {
  if (!data) {
    return null; // Return null if email is not provided
  }
  // Render the email detail item
  return (
    <View style={[style.card, containerStyle]}>
      <Text style={[styles.smallHeading, headingStyle]}>{t(title)}</Text>
      <Text style={[styles.infoText, descriptionStyle]}>{data}</Text>
    </View>
  );
};

export default DetailItem;

const style = StyleSheet.create({
  card: {
    justifyContent: 'center',
    gap: 5,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    padding: 6,
    borderRadius: 14,
    marginBottom: 4,
  },
});
