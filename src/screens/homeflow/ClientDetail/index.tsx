import {
  ActivityIndicator,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useLayoutEffect, useRef, useState} from 'react';
import {EditIcon} from '../../../assets/svgIcons';
import {styles} from './styles';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import PNGIcons from '../../../assets/pngIcons';
import PopUpMenu from '../../../components/PopUpMenu/PopUpMenu';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import ProgressBarComponent from '../../../components/ProgressBarComponent/ProgressBarComponent';
import ClientAuthServices from '../../../services/ClientAuthServices';
import {AxiosError} from 'axios';
import {IUserInfo} from '../../../interfaces/IUser.';
import {useFocusEffect} from '@react-navigation/native';
import {theme} from '../../../utilities/theme';
import {useTranslation} from 'react-i18next';
import {
  netCaloriePercentage,
  netCaloriesLeft,
} from '../../../utilities/app.utils';
import DetailItem from './DetailItem';
type Props = NativeStackScreenProps<HomeStackParamList, 'ClientDetail'>;

export const GOAL_DEFAULT_VALUE: IUserInfo = {
  goal: {
    _id: '',
    calories: 0,
    carbs: 0,
    carbsPercentage: 0,
    client: '',
    coach: '',
    createdAt: '',
    fat: 0,
    fatPercentage: 0,
    proteins: 0,
    proteinsPercentage: 0,
    updatedAt: '',
  },
  usedData: {usedCalories: 0, usedCarbs: 0, usedFats: 0, usedProtiens: 0},
};

const ClientDetail: React.FC<Props> = ({navigation, route}) => {
  const circularProgressRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [userInfo, setUserInfo] = useState<IUserInfo>(GOAL_DEFAULT_VALUE);
  const [chatsList, setChatsList] = useState();
  const [loading, setLoading] = useState(true);

  const {t} = useTranslation();

  const {details} = route.params;

  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <>
          <TouchableOpacity
            style={{marginRight: 10}}
            onPress={() =>
              navigation.navigate('ClientHistory', {clientId: details._id})
            }>
            <Image
              source={PNGIcons.HistoryIcon}
              style={{width: 24, height: 24}}
            />
          </TouchableOpacity>
          <PopUpMenu
            opened={isOpen}
            setOpened={setIsOpen}
            data={[
              {
                id: 1,
                label: t('Archive'),
                onPress: () => navigation.navigate('Archive'),
              },
              {
                id: 2,
                label: t('Delete'),
                onPress: () => setModalVisible(true),
                style: styles.deleteText,
              },
            ]}
          />
        </>
      ),
    });
  }, [navigation, isOpen]);

  async function getClientInfo() {
    try {
      const resp = await ClientAuthServices.GetClientById(details._id);

      // SUCCESS GET
      if (resp.status === 200) {
        setChatsList(resp.data?.client?.chats);
        setUserInfo({
          goal: resp.data?.goal,
          usedData: resp.data.usedData,
        });
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('-- HOME: CLIENT > GET CLIENT BY ID', err);
    } finally {
      setLoading(false);
    }
  }

  useFocusEffect(
    useCallback(() => {
      getClientInfo();
    }, []),
  );

  if (loading) {
    return (
      <View style={{flex: 1, backgroundColor: theme.lightColors?.background}}>
        <ActivityIndicator
          color={theme.lightColors?.primary}
          size={'large'}
          style={{marginTop: 60}}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 30}}>
        <Image
          source={details.image ? {uri: details.image} : PNGIcons.User}
          style={styles.profileImage}
          resizeMode="contain"
        />
        <Text style={styles.nameText}>{details.name}</Text>
        <View style={styles.divider} />
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{t('Daily Goals')}</Text>
          <TouchableOpacity
            hitSlop={4}
            onPress={() =>
              navigation.navigate('EditGoals', {
                clientId: details._id,
                currentGoal: userInfo?.goal,
              })
            }>
            <EditIcon />
          </TouchableOpacity>
        </View>
        <View style={{alignItems: 'center', marginTop: 16}}>
          <CircularProgressBase
            initialValue={1}
            ref={circularProgressRef}
            activeStrokeWidth={10}
            inActiveStrokeWidth={10}
            inActiveStrokeOpacity={0.2}
            value={netCaloriePercentage(userInfo.goal, userInfo.usedData)}
            radius={69}
            activeStrokeColor={theme.lightColors?.primary}
            inActiveStrokeColor={'#000000'}
            strokeLinecap={'round'}
            maxValue={100}>
            <Text style={styles.calProgess}>
              {netCaloriesLeft(userInfo.goal, userInfo.usedData) || 0}
            </Text>
            <Text style={styles.calLeftText}>{t('calories')}</Text>
          </CircularProgressBase>
        </View>

        <ProgressBarComponent
          proteinTotal={userInfo?.goal?.proteins}
          protenIntake={userInfo?.usedData?.usedProtiens}
          carbsTotal={userInfo?.goal?.carbs}
          carbsIntake={userInfo?.usedData?.usedCarbs}
          fatsTotal={userInfo?.goal?.fat}
          fatsIntake={userInfo?.usedData?.usedFats}
        />
        <View style={styles.divider} />
        <DetailItem data={details.email} title={t('Email')} />
        {/* <DetailItem data={ ''} title={t('Phone')} /> //TODO: Add phone number to details */}
        <DetailItem data={details.dob || ''} title={t('Age')} />
        <DetailItem data={details.gender || ''} title={t('Gender')} />
        <DetailItem data={details.goal || ''} title={t('Goal')} />
        <DetailItem data={details.weight || ''} title={t('Current Weight')} />
        {details.goalWeight && (
          <DetailItem data={details.goalWeight} title={t('Goal Weight')} />
        )}
        <DetailItem data={details.height || ''} title={t('Height')} />
        <DetailItem
          data={details.howActive || ''}
          title={t('Activity Level')}
        />
      </ScrollView>
      <TouchableOpacity
        style={styles.conversationContainer}
        onPress={() => {
          const chat = chatsList?.find(c => c.users.includes(details._id));
          navigation.navigate('ChatDetails', {
            recipient: details,
            chatId: chat?._id,
          });
        }}>
        <Image
          source={PNGIcons.Conversation}
          style={styles.conversationImage}
        />
      </TouchableOpacity>
      <GeneralModal
        visible={isModalVisible}
        onCancel={() => setModalVisible(false)}
        primaryButtonOnpress={() => setModalVisible(false)}
        topRedTitle={t('Delete Client')}
        description={t('You are attempting to delete client.')}
        primaryButtonName={t('Yes, Delete')}
        secondaryButtonText={t('Cancel')}
      />
    </View>
  );
};

export default ClientDetail;
