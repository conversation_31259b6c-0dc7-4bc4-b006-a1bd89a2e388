import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  textIcon: {height: 18, width: 18, marginRight: 8},
  calendarRightIcon: {
    position: 'absolute',
    top: -90,
    right: 20,
    tintColor: theme.lightColors?.white,
  },
  calendarLeftIcon: {
    position: 'absolute',
    top: -90,
    right: -1000,
    tintColor: theme.lightColors?.white,
  },
  dayContainer: {
    backgroundColor: theme.lightColors?.grey2,
    marginLeft: 15,
    height: 61,
    borderRadius: 7,
    width: 53,
  },
  dateName: {
    fontSize: 13,
    color: '#2C2C2E2E',
    fontFamily: Fonts.semiBold,
  },
  monthNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 21,
  },
  monthName: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.black,
    textTransform: 'capitalize',
  },
  emptyText: {
    color: '#00000080',
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 12,
  },
  summaryContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    // marginHorizontal: 16,
    padding: 16,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  summaryLabel: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
  summaryValue: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
});
