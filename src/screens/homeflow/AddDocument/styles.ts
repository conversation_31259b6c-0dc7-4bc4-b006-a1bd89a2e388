import { StyleSheet } from 'react-native';
import { Fonts, theme } from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  uploadText: {
    color: '#2C2C2E80',
    fontSize: 12,
    fontFamily: Fonts.regular,
    lineHeight: 16,
    marginTop: 4,
  },
  uploadContainer: {
    paddingVertical: 22,
    backgroundColor: '#FAFAFA',
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
  },
  fileName: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    textAlign: 'center',
    color: theme.lightColors?.grey4
  },
  fileSize: {
    fontFamily: Fonts.regular,
    fontSize: 10,
    textAlign: 'center',
    color: theme.lightColors?.grey4
  },
  errorText: {
    fontFamily: Fonts.regular,
    fontSize: 12,
    color: 'red',
    marginTop: 5,
  },
});
