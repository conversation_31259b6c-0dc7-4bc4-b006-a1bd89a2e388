import {StyleSheet, View} from 'react-native';
import React from 'react';
import {Avatar, ListItem} from '@rneui/base';
import {Fonts, theme} from '../../../utilities/theme';
import {IMealPost} from '../../../interfaces/IMeal';

interface Props {
  client: IMealPost;
  onAddReviewPress: () => void;
}

const ClientCard: React.FC<Props> = ({onAddReviewPress, client}) => {
  return (
    <View style={styles.container}>
      {/* CLIENT INFO */}
      <ListItem containerStyle={styles.profileInfo}>
        <Avatar
          rounded
          source={{uri: 'https://randomuser.me/api/portraits/men/33.jpg'}}
          size={40}
        />
        <ListItem.Content>
          <ListItem.Subtitle style={styles.dateText}></ListItem.Subtitle>
        </ListItem.Content>
      </ListItem>
    </View>
  );
};

export default ClientCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.lightColors?.white,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    padding: 12,
    marginBottom: 12,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
  dateText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
  },
  profileInfo: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  breakFastTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 20,
    height: 20,
  },
  breakfastText: {
    fontSize: 14,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
    paddingLeft: 6,
    marginTop: 2,
  },
  detailsText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
    marginTop: 12,
  },
  itemText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
    paddingLeft: 6,
  },
  calgoriesContainer: {},
  totalText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
    textAlign: 'center',
    paddingVertical: 12,
  },
  totalText2: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.primary,
  },
  nutritionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 32,
  },
  button: {
    width: 120,
    height: 30,
    paddingVertical: 0,
    borderRadius: 6,
    alignSelf: 'flex-end',
    marginTop: 20,
    backgroundColor: `${theme.lightColors?.primary}20`,
  },
  titleStyle: {
    fontSize: 14,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.regular,
  },
});
