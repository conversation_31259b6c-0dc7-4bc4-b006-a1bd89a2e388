import {Skeleton} from '@rneui/base';
import React from 'react';
import {View, StyleSheet} from 'react-native';

const LoadingMealPost = () => {
  return (
    <View style={styles.container}>
      <View style={styles.profileContainer}>
        <Skeleton circle height={40} width={40} />
        <View style={styles.profileText}>
          <Skeleton width={120} height={10} style={styles.marginBottom} />
          <Skeleton width={80} height={8} />
        </View>
      </View>

      <Skeleton width={100} height={16} style={styles.marginBottom} />

      <View>
        <Skeleton width="50%" height={10} style={styles.marginBottom} />
        <Skeleton width="60%" height={10} style={styles.marginBottom} />
        <Skeleton width="70%" height={10} style={styles.marginBottom} />
      </View>

      <Skeleton width="100%" height={1} style={styles.separator} />

      <View style={{alignItems: 'center', justifyContent: 'center'}}>
        <Skeleton width="40%" height={16} style={[styles.marginBottom]} />
      </View>

      <View style={styles.macrosContainer}>
        <Skeleton width={60} height={30} style={styles.marginRight} />
        <Skeleton width={60} height={30} style={styles.marginRight} />
        <Skeleton width={60} height={30} />
      </View>
      <View style={{alignItems: 'flex-end'}}>
        <Skeleton width="30%" height={24} style={styles.marginTop} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
    borderColor: '#ddd',
    borderRadius: 10,
    borderWidth: 1,
    marginTop: 16,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  profileText: {
    marginLeft: 8,
  },
  marginBottom: {
    marginBottom: 6,
  },
  detailsContainer: {
    marginBottom: 0,
  },
  separator: {
    marginTop: 10,
    marginBottom: 16,
  },
  macrosContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  marginRight: {
    marginRight: 14,
  },
  marginTop: {
    marginTop: 10,
  },
});

export default LoadingMealPost;
