import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Image, Divider, ListItem} from '@rneui/base';
import {Fonts, theme} from '../../../utilities/theme';
import PNGIcons from '../../../assets/pngIcons';
import {Check, DotIcon, EditIconFilled, Trash} from '../../../assets/svgIcons';
import Nutritioninfo from './Nutritioninfo';
import {Button} from '../../../components';
import moment from 'moment';
import {IMealPost} from '../../../interfaces/IMeal';
import {AddReviewModal} from '../../../components';
import {useAppDispatch, useAppSelector} from '../../../store';
import MealServices from '../../../services/MealServices';
import {AxiosError} from 'axios';
import {Logger} from '../../../utilities/api.handlers';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {useTranslation} from 'react-i18next';
import {updateMealPostAction} from '../../../store/mealPostSlice';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import useApiHandler from '../../../utilities/useApiHandler';

interface Props {
  mealPost: IMealPost;
  onAddReviewSuccess: () => void;
}

const MealPost: React.FC<Props> = ({onAddReviewSuccess, mealPost}) => {
  const user = useAppSelector(state => state.user);
  const {t, i18n} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const dispatch = useAppDispatch();

  // ADD REVIEW - MODAL HANDLERS
  const [isLoading, setLoading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const handleShowModal = () => {
    if (!mealPost.review?.text) {
      setModalVisible(true);
    }
  };

  // MODAL HANDLERS - DELETE REVIEW
  const [delLoading, setDelLoading] = useState(false);
  const [delVisible, setDelVisible] = useState(false);
  const handleShowDel = () => setDelVisible(true);
  const handleCloseDel = () => setDelVisible(false);

  // RETURNS ICON W.R.T TO MEAL TYPE
  const mealIconType = () => {
    if (mealPost?.mealTitle?.toLowerCase() === 'breakfast') {
      return PNGIcons.BreakFast;
    } else if (mealPost?.mealTitle?.toLowerCase() === 'lunch') {
      return PNGIcons.Lunch;
    } else if (mealPost?.mealTitle?.toLowerCase() === 'dinner') {
      return PNGIcons.Dinner;
    } else if (mealPost?.mealTitle?.toLowerCase() === 'snacks') {
      return PNGIcons.Snacks;
    }
  };

  // COACH ADD A REVIEW FOR THE CLIENT'S MEAL
  async function addReviewFood(text?: string, isReacted?: boolean) {
    setModalVisible(false);

    // UPDATE THE EXSTING REVIEW
    if (mealPost.review?._id) {
      try {
        setLoading(true);
        dispatch(
          updateMealPostAction({
            ...mealPost,
            review: {
              _id: mealPost.review._id,
              text: text,
              coach: user.user._id,
              client: mealPost?.client?._id,
              meal: mealPost?._id,
              isReacted: !!isReacted,
            },
          }),
        );
        const updatePayload = {
          text,
          isReacted: !!isReacted,
        };
        const resp = await MealServices.UpdateReviewForMeal(
          mealPost.review?._id,
          updatePayload,
        );

        // SUCCESS UPDATE
        if (resp.status === 200) {
          onAddReviewSuccess();
          dispatch(
            updateMealPostAction({
              ...mealPost,
              review: {
                _id: mealPost.review?._id,
                text: text,
                coach: user.user._id,
                client: mealPost?.client?._id,
                meal: mealPost?._id,
                isReacted: !!isReacted,
              },
            }),
          );
        }
      } catch (error) {
        const err = error as AxiosError;
        Logger('----- UPDATE REVIEW FOR MEAL ----', err.response?.data);
        handleAxiosErrors(err);
      } finally {
        setLoading(false);
      }
    } else {
      // CREATE A NEW REVIEW FOR THIS MEAL
      try {
        setLoading(true);
        dispatch(
          updateMealPostAction({
            ...mealPost,
            review: {
              _id: 'temp',
              text: text || '',
              isReacted: !!isReacted,
              coach: user.user._id,
              client: mealPost?.client?._id,
              meal: mealPost?._id,
            },
          }),
        );

        const payload = {
          text: text || '',
          isReacted: !!isReacted,
          coach: user.user._id,
          client: mealPost?.client?._id,
          meal: mealPost?._id,
        };

        const resp = await MealServices.AddReviewForMeal(payload);

        // SUCCESS
        if (resp.status === 201) {
          onAddReviewSuccess();
          dispatch(
            updateMealPostAction({
              ...mealPost,
              review: {
                _id: resp.data._id,
                text: resp.data.text || '',
                isReacted: !!resp.data.isReacted,
                coach: resp.data.coach,
                client: resp.data.client,
                meal: resp.data.meal,
              },
            }),
          );
        }
      } catch (error) {
        const err = error as AxiosError;
        Logger('-----ADD REVIEW FOR MEAL ----', err.response?.data);
        handleAxiosErrors(err);
      } finally {
        setLoading(false);
      }
    }
  }

  // COACH DELETES THE REVIEW POSTED ON THE MEAL
  async function deleteReviewOnMeal() {
    setDelLoading(true);
    try {
      const updatedMealPost = {...mealPost, review: null};
      dispatch(updateMealPostAction(updatedMealPost));
      handleCloseDel();
      const resp = await MealServices.DeleteReviewOnMeal(
        mealPost?.review?._id as string,
      );
      // SUCCESS DELETE
      if (resp.status === 200) {
        onAddReviewSuccess();
      }
    } catch (error) {
      const err = error as AxiosError;
      handleAxiosErrors(err);
    } finally {
      setDelLoading(false);
    }
  }

  useEffect(() => {
    moment.locale('en');
  }, [i18n.language]);
  return (
    <View style={styles.container}>
      {/* CLIENT INFO */}
      <ListItem containerStyle={styles.profileInfo}>
        <Image
          source={
            mealPost?.client?.image
              ? {uri: mealPost?.client?.image}
              : PNGIcons.User
          }
          style={styles.userImg}
        />
        <ListItem.Content>
          <ListItem.Title style={styles.name}>
            {mealPost.client.name}
          </ListItem.Title>
          <ListItem.Subtitle style={styles.dateText}>
            {moment(+mealPost.createdAt).isSame(moment(), 'day')
              ? `${t('Today')} - ${moment(+mealPost.createdAt).format(
                  'hh:mm a',
                )}`
              : moment(+mealPost.createdAt).format('MMM DD - hh:mm a')}
          </ListItem.Subtitle>
        </ListItem.Content>
        <TouchableOpacity
          onPress={() =>
            addReviewFood(undefined, !!!mealPost?.review?.isReacted)
          }>
          <Icon
            name={mealPost.review?.isReacted ? 'thumb-up' : 'thumb-up'}
            size={25}
            color={
              mealPost.review?.isReacted
                ? theme.lightColors?.blue
                : theme.lightColors?.grey2
            }
            style={{paddingHorizontal: 8, paddingTop: 15}}
            suppressHighlighting
          />
        </TouchableOpacity>
      </ListItem>

      {/* BREAK FAST TITLE */}
      <View style={[styles.breakFastTitleContainer, {marginTop: 14}]}>
        <Image source={mealIconType()} style={styles.iconContainer} />
        <Text style={styles.breakfastText}>{t(mealPost.mealTitle)}</Text>
      </View>

      {/* LIST OF FOOD IN A MEAL */}
      {mealPost?.mealFoods?.length ? (
        <>
          <Text style={styles.detailsText}>{t('Details')}</Text>

          {mealPost?.mealFoods?.map(item => {
            return (
              <View
                key={item.foodId}
                style={[styles.breakFastTitleContainer, {marginTop: 6}]}>
                <DotIcon width={5} height={5} style={styles.dotIcon} />

                <Text style={styles.itemText}>{item.food?.name}</Text>
              </View>
            );
          })}
        </>
      ) : null}
      {/* DIVIDER */}
      <Divider style={{marginTop: 12}} color={theme.lightColors?.grey2} />
      {/* CALGORIES */}

      <Text style={styles.totalText}>
        Total:{' '}
        <Text style={styles.caloryAmount}>
          {mealPost.totalNutrients.totalCalories || 0}{' '}
          <Text style={{fontSize: 14}}>{t('cal')}</Text>
        </Text>
      </Text>
      {/* NUTRITION INFORMATION */}
      <View style={styles.nutritionContainer}>
        <Nutritioninfo
          nutritionLabel={t('Protein')}
          // weight={`${client.proteins} g`}
          weight={`${mealPost?.totalNutrients?.totalProteins} g`}
        />
        <Nutritioninfo
          nutritionLabel={t('Carbs')}
          weight={`${mealPost?.totalNutrients?.totalCarbs} g`}
          color={theme.lightColors?.primary}
        />
        <Nutritioninfo
          nutritionLabel={t('Fat')}
          weight={`${mealPost?.totalNutrients?.totalFats} g`}
          color={theme.lightColors?.primary}
        />
      </View>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <View />
        {/* ADD REVIEW BUTTONS */}
        <TouchableOpacity style={styles.buttonWrapper}>
          <Button
            title={!mealPost.review?.text ? t('+ Add Reviews') : t('Reviewed')}
            containerStyle={styles.button}
            onPress={handleShowModal}
            titleStyle={styles.titleStyle}
            isShadow={false}
          />
        </TouchableOpacity>
      </View>

      {/* REVIEW */}
      {mealPost?.review?.text ? (
        <View>
          <Divider
            style={{marginVertical: 12}}
            color={theme.lightColors?.grey2}
          />
          <View
            style={{
              flex: 1,
              justifyContent: 'space-between',
              flexDirection: 'row',
            }}>
            <Text style={styles.name}>{t('Reviews')}</Text>
            {isLoading ? null : (
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.editBtn}
                  onPress={() => setModalVisible(true)}>
                  <EditIconFilled width={16} height={16} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.trashBtn}
                  onPress={handleShowDel}>
                  <Trash width={16} height={16} stroke="white" />
                </TouchableOpacity>
              </View>
            )}
          </View>
          <Text style={styles.dateText}>{mealPost?.review?.text}</Text>
        </View>
      ) : null}

      {/* ADD REVIEW MODAL */}
      <AddReviewModal
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        clientImage={mealPost?.client?.image}
        clientName={mealPost?.client?.name}
        mealCreatedAt={mealPost?.createdAt}
        onSubmitReview={(text: string) => addReviewFood(text)}
        isLoading={isLoading}
        reviewText={mealPost?.review?.text}
      />

      {/* DELETE CONFIRMATION - DELETE REVIEW ON MEAL */}
      <GeneralModal
        topRedTitle={t('Confirmation')}
        description={t('You are trying to delete the review.')}
        onCancel={handleCloseDel}
        primaryButtonName={t('Delete')}
        primaryButtonOnpress={deleteReviewOnMeal}
        secondaryButtonText={t('Cancel')}
        visible={delVisible}
        loading={delLoading}
      />
    </View>
  );
};

export default MealPost;

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.lightColors?.white,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    padding: 12,
    marginBottom: 12,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
  dateText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    textTransform: 'capitalize',
  },
  profileInfo: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  breakFastTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 20,
    height: 20,
  },
  breakfastText: {
    fontSize: 14,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
    paddingLeft: 6,
    marginTop: 2,
  },
  detailsText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
    marginTop: 12,
  },
  itemText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
    paddingLeft: 6,
  },
  calgoriesContainer: {},
  totalText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
    textAlign: 'center',
    paddingVertical: 12,
  },
  totalText2: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.primary,
  },
  nutritionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
  },
  button: {
    minWidth: 120,
    height: 32,
    paddingVertical: 0,
    borderRadius: 10,
    alignSelf: 'flex-end',
    marginTop: 12,
    backgroundColor: theme.lightColors?.grey2,
  },
  buttonWrapper: {
    overflow: 'hidden',
    borderRadius: 10,
  },
  titleStyle: {
    fontSize: 14,
    color: theme.lightColors?.black,
    fontFamily: Fonts.regular,
    backgroundColor: undefined,
  },
  editBtn: {
    borderColor: theme.lightColors?.primary,
    borderWidth: 1,
    borderRadius: 4,
    padding: 2,
    marginRight: 8,
  },
  trashBtn: {
    borderRadius: 4,
    padding: 3,
    backgroundColor: '#B4B4B4',
  },
  dotIcon: {
    marginRight: 10,
  },
  buttonContainer: {justifyContent: 'flex-end', flexDirection: 'row'},
  editIcon: {width: 16, height: 16},
  userImg: {width: 40, height: 40, borderRadius: 40 / 2},
  caloryAmount: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.primary,
  },
});
