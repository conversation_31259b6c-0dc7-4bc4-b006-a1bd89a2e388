import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
interface Props {
  color?: string;
  nutritionLabel: string;
  weight: string;
}

const Nutritioninfo: React.FC<Props> = ({color, weight, nutritionLabel}) => {
  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.lightColors?.grey1,
        },
      ]}>
      <Text
        style={[
          styles.nutritionLabelText,
          {color: color ? color : theme.lightColors?.primary},
        ]}>
        {nutritionLabel}
      </Text>
      <Text
        style={[
          styles.text,
          {color: color ? color : theme.lightColors?.primary},
        ]}>
        {weight}
      </Text>
    </View>
  );
};

export default Nutritioninfo;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 10,
    // minWidth: 55,
    width: '30%',
    paddingHorizontal: 8,
    marginBottom: 8,
  },
  nutritionLabelText: {
    fontSize: 12,
    fontFamily: Fonts.regular,
  },
  text: {
    fontSize: 12,
    fontFamily: Fonts.semiBold,
    paddingTop: 4,
  },
});
