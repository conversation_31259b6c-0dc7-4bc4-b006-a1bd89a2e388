import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: '#FFF',
  },
  reviewText: {
    color: '#747474',
    fontSize: 12,
    lineHeight: 15,
    marginTop: 8,
    fontFamily: Fonts.regular,
  },
  buttonBackground: {
    // paddingHorizontal: 40,
    paddingVertical: 8,
    backgroundColor: theme.lightColors?.grey1,
    // width: 104,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  headingText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
  caloryText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.black,
  },

  inputContainer: {
    borderColor: theme.lightColors?.grey1,
    borderRadius: 10,
    borderWidth: 1,
    backgroundColor: '#FAFAFA',
    paddingHorizontal: 10,
    alignSelf: 'center',
    marginTop: 8,
  },
  containerStyles: {
    width: '30%',
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  inputLabel: {
    marginBottom: 12,
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.secondary,
  },
  unitText: {fontFamily: Fonts.regular, color: '#2C2C2E80'},
  modalHeading: {
    marginTop: 20,
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    marginTop: 150,
  },
  deleteFood: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  deleteFoodContainer: {paddingVertical: 11, paddingHorizontal: 27},
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 40,
    marginTop: 10,
  },
  saveButton: {width: '90%', marginLeft: 13},
  cameraIcon: {width: 21, height: 17},
  errorText: {
    fontSize: 12,
    fontFamily: Fonts.regular,
    color: 'red',
  },
  loadingFoods: {
    marginVertical: 24,
  },
  arrowImage: {height: 18, width: 18},
  noFoodImage: {
    height: 120,
    width: 150,
  },
  noFoodHeading: {
    color: '#00000080',
    marginTop: 16,
    fontWeight: '600',
    fontSize: 16,
    fontFamily: Fonts.bold,
  },
  noFoodText: {
    fontSize: 12,
    color: '#00000080',
    marginTop: 8,
    textAlign: 'center',
    width: '70%',
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  coachReviewContainer: {marginVertical: 14, flex: 1},
  coachReviewInnerContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  coachReviewText: {
    color: theme.lightColors?.black,
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    marginBottom: 10,
  },
  arrowIcon: {height: 16, width: 16},
  addText: {
    color: theme.lightColors?.primary,
    // textAlign: 'center',
  },
  addTextContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginVertical: 10,
  },
  textWrapper: {
    backgroundColor: theme.lightColors?.grey1,
    paddingHorizontal: 20,
    paddingVertical: 5,
    borderRadius: 6,
    // width: '100%',
  },
});
