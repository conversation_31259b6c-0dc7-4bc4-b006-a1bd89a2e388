import {Image, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import PNGIcons from '../../../assets/pngIcons';
import NutritionProgress from '../../../components/NutritionProgress/NutritionProgress';
import {styles} from './styles';
import {Button} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<ClientStackParamList, 'EditMeal'>;
const EditMeal: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const {t} = useTranslation();
  return (
    <View style={styles.container}>
      <View>
        <View style={styles.mealList}>
          <Text style={styles.mealName}>{t('Breakfast')}</Text>
          <Image source={PNGIcons.ArrowDown} style={styles.arrowIcon} />
        </View>
        <View style={styles.servingContainer}>
          <View style={[styles.mealList, styles.servingCount]}>
            <Text style={styles.regularFont}>1</Text>
          </View>
          <View style={[styles.mealList, styles.servingList]}>
            <Text style={styles.mealName}>{t('Serving')}</Text>
            <Image source={PNGIcons.ArrowDown} style={styles.arrowIcon} />
          </View>
        </View>
        <Text style={styles.headingText}>{t('Nutritional Information')}</Text>
        <View style={styles.nutritionContainer}>
          <Text style={styles.caloryText}>
            377<Text style={styles.calUnitText}> {t('cal')}</Text>
          </Text>
          <View style={styles.progressIndicator}>
            <NutritionProgress color="#FFA047" />
            <NutritionProgress color="#51A5F3" />
            <NutritionProgress color="#58DF42" />
          </View>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.deleteFoodContainer}
          onPress={() => setModalVisible(true)}>
          <Text style={styles.deleteFood}>{t('Delete Food')}</Text>
        </TouchableOpacity>
        <Button
          title={t('Save')}
          containerStyle={styles.saveButton}
          onPress={() => navigation.goBack()}
        />
      </View>
      <GeneralModal
        visible={isModalVisible}
        onCancel={() => setModalVisible(false)}
        primaryButtonOnpress={() => setModalVisible(false)}
        topRedTitle={t('Delete Food')}
        description={t('You are attempting to delete food.')}
        primaryButtonName={t('Delete')}
        secondaryButtonText={t('Cancel')}
      />
    </View>
  );
};

export default EditMeal;
