import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 24,
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    justifyContent: 'space-between',
  },
  calLeftText: {
    fontFamily: Fonts.regular,
    fontSize: 16,
    lineHeight: 19,
    color: theme.lightColors?.black,
  },
  calProgess: {
    fontFamily: Fonts.bold,
    fontSize: 24,
    color: theme.lightColors?.black,
  },
  typeText: {
    fontFamily: Fonts.bold,
    fontSize: 14,
    lineHeight: 18,
    color: theme.lightColors?.black,
  },
  calText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    lineHeight: 18,
    marginTop: 8,
    color: theme.lightColors?.black,
  },
  totalCal: {
    fontFamily: Fonts.regular,
    fontSize: 12,
    lineHeight: 15,
    color: theme.lightColors?.black,
  },
  heading: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    letterSpacing: 0.5,
    color: theme.lightColors?.secondary,
    marginTop: 35,
  },
  circularProgressContainer: {alignItems: 'center', marginTop: 16},
  verticalProgressContainer: {
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginTop: 30,
    alignItems: 'center',
  },
  goalText: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.grey4,
  },
  contactText: {
    fontFamily: Fonts.semiBold,
    fontSize: 12,
    color: theme.lightColors?.grey4,
    marginTop: 5,
  },
});
