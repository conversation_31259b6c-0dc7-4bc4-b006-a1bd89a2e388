import {View, Text, ScrollView} from 'react-native';
import React from 'react';
import {styles} from './styles';
import {Button, FormInput, SelectOptionModal} from '../../../components';
import {useTranslation} from 'react-i18next';
import {useFormik} from 'formik';
import * as yup from 'yup';
import useActivity from '../../../hooks/models/useActivity';
import {handleApiErrors} from '../../../utilities/api.handlers';
import {IExercise} from '../../../interfaces/IExercise';

interface Props {
  onPressAdd?: () => void;
  onExerciseCreated?: (exerciseId: IExercise) => void;
}

interface FormData {
  title: string;
  //   CARDIO|STRENGTH|LIGHT
  category: 'Cardio' | 'Strength' | 'Light';
  calories: string;
  duration: string;
  sets: string;
  reps: string;
}

export const NewExercise: React.FC<Props> = ({
  onPressAdd,
  onExerciseCreated,
}) => {
  const {t} = useTranslation();
  const {createExercise, isSubmittingExercise} = useActivity();

  const validationSchema = yup.object({
    title: yup.string().required(t('Enter title')),
    calories: yup.string().required(t('Enter calories')),
    duration: yup.string().required(t('Enter duration')),
  });

  const formik = useFormik<FormData>({
    initialValues: {
      title: '',
      category: 'Cardio',
      calories: '',
      duration: '',
      sets: '',
      reps: '',
    },
    onSubmit: async ({
      calories,
      category,
      title,
      duration,
      sets,
      reps,
    }: FormData) => {
      try {
        const exercise = await createExercise({
          calories: parseInt(calories),
          category: category.toUpperCase(),
          title,
          duration: parseInt(duration),
          sets: parseInt(sets),
          reps: parseInt(reps),
        });

        onExerciseCreated && onExerciseCreated(exercise);
      } catch (error: any) {
        handleApiErrors(error);
        console.log('Error in adding exercise', error.response);
      } finally {
        onPressAdd && onPressAdd();
      }
    },
    validationSchema,
    enableReinitialize: true,
  });

  const {calories, category, title, duration, sets, reps} = formik.values;

  return (
    <View style={styles.modalContainer}>
      <ScrollView>
        <Text style={styles.modalHeading}>{t('New Exercise')}</Text>
        <View style={styles.divider} />
        <FormInput
          value={title}
          label={t('Title')}
          onChangeText={formik.handleChange('title')}
          onBlur={formik.handleBlur('title')}
          placeholder={t('Bicycling')}
          errorMessage={formik.touched.title ? formik.errors.title : ''}
        />

        <SelectOptionModal
          label={t('Category')}
          optionArray={['Cardio', 'Strength', 'Light']}
          setValue={val => formik.setFieldValue('category', val)}
          value={category}
        />
        <FormInput
          value={calories}
          label={t('Calories')}
          placeholder={t('200')}
          onChangeText={formik.handleChange('calories')}
          onBlur={formik.handleBlur('calories')}
          keyboardType="number-pad"
          errorMessage={formik.touched.calories ? formik.errors.calories : ''}
        />
        <FormInput
          value={duration}
          label={t('Duration (mins)')}
          placeholder={t('minutes')}
          onChangeText={formik.handleChange('duration')}
          onBlur={formik.handleBlur('duration')}
          keyboardType="number-pad"
          errorMessage={formik.touched.duration ? formik.errors.duration : ''}
        />

        {/* <SelectOptionModal
          label="Sets (optional)"
          optionArray={['1', '2', '3', '4']}
          setValue={val => formik.setFieldValue('sets', val)}
          value={sets}
          showReset
        />

        <SelectOptionModal
          label="Reps (optional)"
          optionArray={['5', '10', '20', '40', '60', '80', '100']}
          setValue={val => formik.setFieldValue('reps', val)}
          value={reps}
          showReset
        /> */}

        <Button
          title={t('Add')}
          containerStyle={styles.saveButton}
          onPress={formik.handleSubmit}
          loading={isSubmittingExercise}
          disabled={isSubmittingExercise}
        />
      </ScrollView>
    </View>
  );
};
