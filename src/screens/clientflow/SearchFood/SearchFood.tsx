import * as React from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {SearchBar} from '../../../components';
import {ScrollView} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';
import {IFood} from '../../../interfaces/IFoods';
import MealSelect from '../../../components/MealSelect/MealSelect';
import Toast from 'react-native-toast-message';
import {useAppDispatch, useAppSelector} from '../../../store';
import {IMealDetails} from '../../../interfaces/IMeal';

import {styles} from '../MealHistory/styles';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {useTranslation} from 'react-i18next';
import useClients from '../../../hooks/models/useClients';
import useMeals from '../../../hooks/models/useMeals';
import {setMealFoods} from '../../../store/MealsSlice';

interface SearchFoodProps {}
type Props = NativeStackScreenProps<ClientStackParamList, 'SearchFood'>;

const SearchFood: React.FC<Props> = ({navigation, route}) => {
  const [searchText, setSearchText] = React.useState('');
  const [searchedFoods, setSearchFoods] = React.useState<IFood[]>([]);
  const [selectedFoods, setSelectedFoods] = React.useState<IFood[]>(
    route.params?.mealFoods || [],
  );

  const {t} = useTranslation();
  const {fetchClient} = useClients();
  const {updateMealFoods, isSubmitting, createMeal, getMealDetails} =
    useMeals();

  const dispatch = useAppDispatch();
  const foodsStore = useAppSelector(state => state.foods);
  const {mealFoods} = useAppSelector(state => state.meals);
  const user = useAppSelector(state => state.user.user);

  function handleSearchFood() {
    if (searchText) {
      const arrOfSearched = foodsStore.filter(item =>
        item.name.toLowerCase().includes(searchText.toLowerCase()),
      );
      setSearchFoods(arrOfSearched as IFood[]);
    } else {
      setSearchFoods(foodsStore);
    }
  }

  React.useEffect(() => {
    handleSearchFood();
  }, [searchText]);

  async function handleSubmit() {
    try {
      let meal: IMealDetails;
      if (!route.params.mealId) {
        meal = await createMeal(route.params.mealTitle);
      } else {
        meal = await getMealDetails(route.params.mealId);
      }

      if (meal) {
        // updating on backend
        await updateMealFoods(meal, [...selectedFoods]);
      }
      // await updateMealFoods(selectedFoods);
      dispatch(setMealFoods([...selectedFoods]));
      // Fetching client again to update home stats
      fetchClient();

      setTimeout(() => {
        navigation.goBack();
      }, 100);
    } catch (_error) {
    } finally {
    }
  }

  // Callback for on press plus icon
  const onPressAddFood = (thisFood: IFood) => {
    let alreadyAdded = selectedFoods.find(item => item._id === thisFood._id);
    if (alreadyAdded) {
      setSelectedFoods(selectedFoods.filter(item => item._id !== thisFood._id));

      //   addFoodToMeal(selectedFoods.filter(item => item._id !== thisFood._id));
    } else {
      setSelectedFoods([...selectedFoods, thisFood]);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 100}}>
        <SearchBar
          value={searchText}
          // micBtn
          onChangeText={v => setSearchText(v)}
        />

        {/* SEARCH FOODS */}
        <View>
          {searchText ? (
            <Text
              style={{
                marginBottom: 8,
                fontFamily: Fonts.regular,
                color: theme.lightColors?.black,
              }}>
              {t('Results found for')}{' '}
              <Text
                style={{
                  fontFamily: Fonts.semiBold,
                  color: theme.lightColors?.black,
                }}>
                “{searchText}”
              </Text>
            </Text>
          ) : null}

          {searchedFoods.map((result, index) => (
            <MealSelect
              key={index}
              name={result.name}
              calory={result.calories.toString()}
              tickIcon={
                selectedFoods.find(item => item._id === result._id)
                  ? true
                  : false
              }
              onPress={() => {
                onPressAddFood(result);
              }}
              nameTick
            />
          ))}
        </View>
      </ScrollView>

      {selectedFoods.length ? (
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={handleSubmit}
          style={{
            backgroundColor: theme.lightColors?.primary,
            position: 'absolute',
            bottom: 40,
            paddingVertical: 12,
            paddingHorizontal: 16,
            marginHorizontal: 24,
            width: '100%',
            borderRadius: 10,
            flexDirection: 'row',
            justifyContent: 'space-between',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.22,
            shadowRadius: 2.22,

            elevation: 3,
          }}>
          <Text
            style={{color: '#fff', fontFamily: Fonts.semiBold, fontSize: 16}}>
            {t('Add to')} {route.params.mealTitle}
          </Text>
          {isSubmitting ? (
            <ActivityIndicator size="small" color={'#FFF'} />
          ) : (
            <Text
              style={{color: '#fff', fontFamily: Fonts.semiBold, fontSize: 16}}>
              {selectedFoods.length}
            </Text>
          )}
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

export default SearchFood;
