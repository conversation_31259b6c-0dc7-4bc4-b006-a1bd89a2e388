import {
  ActivityIndicator,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>er} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {SafeAreaView} from 'react-native';
import {theme} from '../../../utilities/theme';
import {Image} from 'react-native';
import PNGIcons from '../../../assets/pngIcons';
import ClientHomeCalory from '../../../components/ClientHomeCalory/ClientHomeCalory';
import WeightTrackerGraph from '../../../components/WeightTrackerGraph/WeightTrackerGraph';
import Modal from 'react-native-modal';
import {styles} from './styles';
import {useAppDispatch, useAppSelector} from '../../../store';
import {AxiosError} from 'axios';
import moment, {Moment} from 'moment';
import {IFood} from '../../../interfaces/IFoods';
import FoodServices from '../../../services/FoodServices';
import {setFoods} from '../../../store/FoodsSlice';
import {useFocusEffect} from '@react-navigation/native';
import images from '../../../assets/images';
import SystemNavigationBar from 'react-native-system-navigation-bar';
import {WaterIntake} from './WaterIntake';
import {useTranslation} from 'react-i18next';
import useClients from '../../../hooks/models/useClients';
import useStatistics from '../../../hooks/models/useStatistics';

import useMeals from '../../../hooks/models/useMeals';
import {DailyIntakeStats} from './DailyIntakeStats';
import WeightForm from './WeightForm';
import {IWeight} from '../../../interfaces/IWeight';
import CalendarStrip from '../../../components/CalendarStrip/CalendarStrip';

type Props = NativeStackScreenProps<ClientStackParamList, 'BottomTabNavigator'>;

const ClientHome: React.FC<Props> = ({navigation}) => {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const user = useAppSelector(state => state.user.user);

  const [isModal, setIsModal] = useState(false);

  const [date, setDate] = useState<Moment>(moment());

  const [clickedWeight, setClickedWeight] = useState<IWeight>();

  const {dailyIntakeGoal, dailyMacroNutrients} = useClients();
  const {weights} = useStatistics();
  const {fetchMeals, isLoading} = useMeals();
  const {stepCount, dailyActivity} = useAppSelector(state => state.user);

  useEffect(() => {
    if (date) fetchMeals(date);
  }, [date]);

  const incrementDate = () => {
    setDate(moment(date).add(1, 'days'));
  };

  const decrementDate = () => {
    setDate(moment(date).subtract(1, 'days'));
  };

  // GET FOODS
  async function getFoodsForClient() {
    try {
      const resp = await FoodServices.GetFoods();
      // SUCCESS GET
      if (resp.status === 200) {
        const foodsData = resp.data.foods as IFood[];
        dispatch(setFoods(foodsData));
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log(err, '-- GET FOODS FOR CLIENT --');
    }
  }

  useFocusEffect(
    useCallback(() => {
      getFoodsForClient();
    }, []),
  );

  useEffect(() => {
    SystemNavigationBar.setNavigationColor(theme.lightColors?.primary || '');
  }, []);

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 20}}>
        <Header
          onBellPress={() => navigation.navigate('Notifications')}
          onAvatarPress={() => navigation.navigate('Profile')}
          unread
        />

        {dailyIntakeGoal ? (
          <>
            <DailyIntakeStats />

            <CalendarStrip
              date={date}
              setDate={setDate}
              onPressLeftArrow={decrementDate}
              onPressRightArrow={incrementDate}
            />
            {/* FOOD LIST */}
            {isLoading ? (
              <ActivityIndicator
                color={theme.lightColors?.primary}
                size={'large'}
                style={{marginVertical: 140}}
              />
            ) : (
              <>
                <ClientHomeCalory
                  value="Breakfast"
                  image={PNGIcons.BreakFast}
                  date={date}
                />
                <ClientHomeCalory
                  value="Lunch"
                  image={PNGIcons.Lunch}
                  date={date}
                />
                <ClientHomeCalory
                  value="Dinner"
                  image={PNGIcons.Dinner}
                  date={date}
                />
                <ClientHomeCalory
                  value="Snacks"
                  image={PNGIcons.Snacks}
                  date={date}
                />
              </>
            )}

            {/* --- WATER INTAKE --- */}
            <WaterIntake />

            <View style={styles.stepActivity}>
              {/* STEPS COUNT */}
              <View style={styles.stepsContainer}>
                <View style={styles.stepTextContainer}>
                  <Image
                    source={PNGIcons.Steps}
                    style={[styles.stepIcon, {tintColor: '#000000'}]}
                    resizeMode="contain"
                  />
                  <Text style={styles.stepText}>{t('Steps')}</Text>
                </View>
                <Text style={styles.stepNumber}>{stepCount}</Text>
              </View>

              {/* ACTIVITY */}
              <TouchableOpacity
                style={styles.stepsContainer}
                onPress={() => navigation.navigate('Activity')}>
                <View style={styles.stepTextContainer}>
                  <Image
                    source={PNGIcons.ActivityIcon}
                    style={[styles.stepIcon, {tintColor: '#000000'}]}
                  />
                  <Text style={styles.stepText}>{t('Activity')}</Text>
                </View>
                <View
                  style={[styles.stepTextContainer, styles.activityContainer]}>
                  <Image source={PNGIcons.TimeIcon} style={styles.timeIcon} />
                  <Text style={styles.minText}>
                    {dailyActivity?.totalDuration || 0} min
                  </Text>
                </View>
                <View
                  style={[styles.stepTextContainer, styles.activityContainer]}>
                  <Image
                    resizeMode="contain"
                    source={PNGIcons.RedIcon}
                    style={[
                      styles.timeIcon,
                      {tintColor: '#F64144', width: 10, height: 14},
                    ]}
                  />
                  <Text style={styles.minText}>
                    {dailyActivity?.totalCaloriesBurned || 0} {t('cal')}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* WEIGHT TRACKING GRAPH */}
            <WeightTrackerGraph
              client={true}
              weights={weights}
              onPress={() => {
                setClickedWeight(undefined);
                setIsModal(true);
              }}
              onPressWeight={weight => {
                setClickedWeight(weight);
                setIsModal(true);
              }}
            />
          </>
        ) : (
          <View style={styles.coachContainer}>
            <Text style={styles.statsText} numberOfLines={2}>
              {t('Statistics not available')}
            </Text>
            <View style={styles.goalContainer}>
              <View style={styles.goalInnerContainer}>
                <Text style={styles.goalText}>{t('Goal')}</Text>
                <Text style={styles.questionText} numberOfLines={3}>
                  {t(
                    'Please ask your coach to set you a diet goal to see your progress',
                  )}
                </Text>
                <Button
                  title={t('Chat Now')}
                  containerStyle={styles.chatButton}
                  onPress={() => {
                    const coach = user?.coach;
                    if (coach) {
                      const chat = user?.chats?.find(c =>
                        c.users.includes(coach._id),
                      );
                      navigation.navigate('ChatDetails', {
                        recipient: user.coach,
                        chatId: chat?._id,
                      });
                    }
                  }}
                />
              </View>
              <Image source={images.noGoal} style={styles.noGoalImage} />
            </View>
          </View>
        )}
      </ScrollView>

      {/* ADD WEIGHT MODAL */}
      <Modal
        isVisible={isModal}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setIsModal(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{margin: 0, justifyContent: 'flex-end'}}>
        <View style={styles.modalContainer}>
          <WeightForm
            onCancel={() => setIsModal(false)}
            onSubmitted={() => setIsModal(false)}
            weight={clickedWeight}
          />
        </View>
      </Modal>
    </View>
  );
};

export default ClientHome;
