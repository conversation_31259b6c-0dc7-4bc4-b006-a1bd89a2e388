import {Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import Toast from 'react-native-toast-message';
import {Image} from 'react-native';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import {useTranslation} from 'react-i18next';
import {useFormik} from 'formik';
import {AxiosError} from 'axios';

import {Button, FormInput} from '../../../components';
import {theme} from '../../../utilities/theme';
import PNGIcons from '../../../assets/pngIcons';
import {styles} from './styles';
import {addNewWeight} from '../../../store/StatisticsSlice';
import {updateUser} from '../../../store/userSlice';
import {useAppDispatch, useAppSelector} from '../../../store';
import {addWeightSchema} from './addWeight.schema';
import WeightServices from '../../../services/WeightServices';
import {IAddWeightFormData, IWeight} from '../../../interfaces/IWeight';
import images from '../../../assets/images';
import useStatistics from '../../../hooks/models/useStatistics';

interface Props {
  onCancel?: () => void;
  onSubmitted?: () => void;
  weight?: IWeight;
}

const WeightForm: React.FC<Props> = ({weight, onCancel, onSubmitted}) => {
  const dispatch = useAppDispatch();
  const {t, i18n} = useTranslation();
  const user = useAppSelector(state => state.user.user);

  const [sideImage, setSideImage] = useState<ImageOrVideo>();
  const [backImage, setBackImage] = useState<ImageOrVideo>();
  const [frontImage, setFrontImage] = useState<ImageOrVideo>();
  const [weightLoading, setWeightLoading] = useState(false);

  const pickImage = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        compressImageQuality: 0.25,
        mediaType: 'photo',
      });
      return image;
    } catch (error) {
      console.log('Error selecting image', error);
    }
  };

  const handleSideImage = async () => {
    const imageUri = await pickImage();
    if (imageUri) {
      setSideImage(imageUri);
      formik.setFieldValue('side', imageUri.path);
    }
  };
  const handleBackImage = async () => {
    const imageUri = await pickImage();
    if (imageUri) {
      setBackImage(imageUri);
      formik.setFieldValue('back', imageUri.path);
    }
  };
  const handleFrontImage = async () => {
    const imageUri = await pickImage();
    if (imageUri) {
      setFrontImage(imageUri);
      formik.setFieldValue('front', imageUri.path);
    }
  };

  // API FUNCTION - UPLOAD WEIGHT IMAGE
  async function uploadWeightImage() {
    if (sideImage && frontImage && backImage) {
      try {
        const formData = new FormData();
        formData.append('side', {
          uri: Platform.OS == 'ios' ? sideImage?.sourceURL : sideImage?.path,
          type: sideImage?.mime,
          name: `side`,
        });
        formData.append('front', {
          uri: Platform.OS == 'ios' ? frontImage?.sourceURL : frontImage?.path,
          type: frontImage?.mime,
          name: `front`,
        });
        formData.append('back', {
          uri: Platform.OS == 'ios' ? backImage?.sourceURL : backImage?.path,
          type: backImage?.mime,
          name: `back`,
        });

        const resp = await WeightServices.UploadImagesForWeight(formData);
        if (resp.status == 201) {
          let photos = resp.data?.map((item: any) => {
            return {key: item.key, value: item.value};
          });
          return photos;
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log('----------- upload images:', err.response?.data);
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t("Couldn't upload the images."),
        });
      }
    }
  }
  const {fetchWeights} = useStatistics();
  // API FUNCTION - ADD WEIGHT
  async function addWeightProgress(values: IAddWeightFormData) {
    setWeightLoading(true);
    const payload = {
      value: values.weight,
      unit: values.unit,
    };

    dispatch(updateUser({...user, _id: user._id, weight: values.weight}));
    try {
      const uploadedImage = await uploadWeightImage();

      const resp = await WeightServices.AddWeight({
        ...payload,
        photos: uploadedImage,
      });

      // SUCCESS - WEIGHT ADDED
      if (resp.status === 201) {
        onSubmitted && onSubmitted();
        fetchWeights();
        Toast.show({
          type: 'success',
          text1: t('Success'),
          text2: t('Weight progress added successfully'),
        });
        formik.resetForm();
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('CLIENT > ADD WEIGHT -> ', err);
    }
    setWeightLoading(false);
  }

  const {front, back, side} = React.useMemo(() => {
    const front = weight?.photos.find(p => p.key === 'front');
    const back = weight?.photos.find(p => p.key === 'back');
    const side = weight?.photos.find(p => p.key === 'side');

    return {front, back, side};
  }, [weight]);

  // ADD WEIGHT FORMIK
  const formik = useFormik({
    initialValues: {
      weight: weight?.value?.toString() || '',
      unit: weight?.unit || 'kg',
      front: front?.url || '',
      side: side?.url || '',
      back: back?.url || '',
    },
    enableReinitialize: true,
    validationSchema: addWeightSchema,
    onSubmit: addWeightProgress,
  });

  return (
    <ScrollView>
      <View style={{marginBottom: 40}}>
        <View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: 20,
            }}>
            <View />
            <Text style={styles.modalHeading}>
              {!!weight ? t('View Weight') : t('Add Weight')}
            </Text>
            <TouchableOpacity onPress={onCancel}>
              <Image
                source={PNGIcons.CrossIcon}
                style={{height: 24, width: 24}}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.divider} />
          <FormInput
            label={t('Enter your weight')}
            keyboardType="decimal-pad"
            value={formik.values.weight}
            onChangeText={formik.handleChange('weight')}
            onBlur={formik.handleBlur('weight')}
            errorMessage={
              formik.touched.weight
                ? t(formik.errors.weight as string)
                : undefined
            }
            disabled={!!weight}
          />

          {/* UNITS */}
          <View style={styles.units}>
            <TouchableOpacity
              style={[
                styles.unitContainer,
                {
                  backgroundColor:
                    formik.values.unit === 'kg'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              disabled={!!weight}
              onPress={() => formik.setFieldValue('unit', 'kg')}>
              <Text
                style={{
                  color:
                    formik.values.unit === 'kg'
                      ? theme.lightColors?.white
                      : '#969696',
                }}>
                kg
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.unitContainer,
                {
                  backgroundColor:
                    formik.values.unit === 'lb'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              disabled={!!weight}
              onPress={() => formik.setFieldValue('unit', 'lb')}>
              <Text
                style={{
                  color:
                    formik.values.unit === 'lb'
                      ? theme.lightColors?.white
                      : '#969696',
                }}>
                lb
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.imageContainer}>
            {/* FRONT IMAGE */}
            {!weight || front?.url ? (
              <View style={styles.imageWidth}>
                <Text style={styles.uploadText}>{t('Front image')}</Text>
                <TouchableOpacity
                  style={styles.uploadContainer}
                  disabled={!!weight}
                  onPress={handleFrontImage}>
                  {formik.values.front ? (
                    <Image
                      source={{uri: formik.values.front}}
                      style={styles.selectedImage}
                    />
                  ) : (
                    <Image
                      source={PNGIcons.UploadImage}
                      style={styles.uploadIcon}
                    />
                  )}
                </TouchableOpacity>
              </View>
            ) : null}

            {/* SIDE IMAGE */}
            {!weight || side?.url ? (
              <View style={styles.sideImage}>
                <Text style={styles.uploadText}>{t('Side image')}</Text>
                <TouchableOpacity
                  style={styles.uploadContainer}
                  disabled={!!weight}
                  onPress={handleSideImage}>
                  {formik.values.side ? (
                    <Image
                      source={{uri: formik.values.side}}
                      style={styles.selectedImage}
                    />
                  ) : (
                    <Image
                      source={PNGIcons.UploadImage}
                      style={styles.uploadIcon}
                    />
                  )}
                </TouchableOpacity>
              </View>
            ) : null}

            {/* BACK IMAGE */}
            {!weight || back?.url ? (
              <View style={styles.imageWidth}>
                <Text style={styles.uploadText}>{t('Back image')}</Text>
                <TouchableOpacity
                  style={styles.uploadContainer}
                  disabled={!!weight}
                  onPress={handleBackImage}>
                  {formik.values.back ? (
                    <Image
                      source={{uri: formik.values.back}}
                      style={styles.selectedImage}
                    />
                  ) : (
                    <Image
                      source={PNGIcons.UploadImage}
                      style={styles.uploadIcon}
                    />
                  )}
                </TouchableOpacity>
              </View>
            ) : null}
          </View>
        </View>

        {/* ACTION BUTTONS */}
        {weight ? null : (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.deleteFoodContainer}
              onPress={() => {
                onCancel && onCancel();
                formik.resetForm();
              }}>
              <Text style={styles.deleteFood}>{t('Cancel')}</Text>
            </TouchableOpacity>
            <Button
              title={t('Add')}
              containerStyle={styles.saveButton}
              onPress={formik.handleSubmit}
              loading={weightLoading}
              disabled={!formik.isValid || weightLoading}
            />
          </View>
        )}
      </View>
    </ScrollView>
  );
};

export default WeightForm;
