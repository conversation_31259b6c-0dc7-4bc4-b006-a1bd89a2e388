import React from 'react';
import View from '../../../components/View';
import {Image, TouchableOpacity} from 'react-native';
import {Text} from 'react-native';
import PNGIcons from '../../../assets/pngIcons';
import {styles} from './styles';
import {Fonts, theme} from '../../../utilities/theme';
import WaterService from '../../../services/WaterService';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import useStatistics from '../../../hooks/models/useStatistics';
import {useAppDispatch, useAppSelector} from '../../../store';
import {setWaterIntake} from '../../../store/StatisticsSlice';

export const WaterIntake = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {waterIntake} = useStatistics();

  const user = useAppSelector(state => state.user.user);

  async function addWater(glasses: number) {
    const reset = glasses === waterIntake?.value;
    const newGlasses = glasses <= waterIntake?.value ? glasses - 1 : glasses;
    const waterPayload = {
      value: newGlasses,
      unit: 'glass',
      notes: reset ? '' : `Glass No. ${glasses}`,
    };

    dispatch(
      setWaterIntake({
        _id: '',
        client: user._id,
        value: newGlasses,
        createdAt: new Date().toDateString(),
        updatedAt: new Date().toDateString(),
        notes: waterPayload.notes,
        unit: 'ltr',
      }),
    );

    if (!waterIntake?._id || reset) {
      await WaterService.AddWater(waterPayload)
        .then(resp => {
          dispatch(setWaterIntake(resp.data));
        })
        .catch((error: AxiosError) => {
          console.log('CLIENT > ADD WATER INTAKE -> ', error.response?.data);
        });
    } else {
      await WaterService.UpdateWater(waterIntake?._id, waterPayload)
        .then(resp => {
          dispatch(setWaterIntake(resp.data));
        })
        .catch((error: AxiosError) => {
          console.log('CLIENT > UPDATE WATER INTAKE -> ', error.response?.data);
        });
    }
  }

  return (
    <>
      <View style={styles.waterContainer}>
        <View style={styles.waterTextContainer}>
          <View style={styles.waterRow}>
            <Image source={PNGIcons.WaterDrop} style={styles.waterDrop} />
            <Text style={styles.waterText}>{t('Hydratation')}</Text>
          </View>
          <Text style={styles.literText}>
            {waterIntake?.value * 0.25 || 0} L
          </Text>
        </View>
        <View style={styles.glassContainer}>
          {Array.from({length: waterIntake?.value || 0}).map((_item, index) => (
            <TouchableOpacity key={index} onPress={() => addWater(index + 1)}>
              <Image
                source={PNGIcons.FullGlass}
                style={styles.glassIcon}
                key={index}
              />
            </TouchableOpacity>
          ))}

          {Array.from({length: 8 - Number(waterIntake?.value)}).map(
            (_item, index) => (
              <TouchableOpacity
                key={index}
                onPress={() =>
                  addWater(Number(waterIntake?.value) + index + 1)
                }>
                <Image source={PNGIcons.EmptyGlass} style={styles.glassIcon} />
              </TouchableOpacity>
            ),
          )}

          {!waterIntake &&
            Array.from({length: 8}).map((_item, index) => (
              <TouchableOpacity key={index} onPress={() => addWater(index + 1)}>
                <Image source={PNGIcons.EmptyGlass} style={styles.glassIcon} />
              </TouchableOpacity>
            ))}
        </View>
      </View>
    </>
  );
};
