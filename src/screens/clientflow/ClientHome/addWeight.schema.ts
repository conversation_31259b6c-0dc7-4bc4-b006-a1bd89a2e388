import * as yup from "yup";

export const addWeightSchema = yup.object().shape({
  weight: yup.string().required('Weight is required'),
  unit: yup.string().required('Unit is required'),
  front: yup.string().when(['side', 'back'], {
    is: (side: string, back: string) => side || back,
    then: () => yup.string().required("Front image is required"),
    otherwise: () => yup.string().optional()
  }),
  side: yup.string().when(['front', 'back'], {
    is: (front: string, back: string) => front || back,
    then: () => yup.string().required("Side image is required"),
    otherwise: () => yup.string().optional()
  }),
  back: yup.string().when(['front', 'side'], {
    is: (front: string, side: string) => front || side,
    then: () => yup.string().required("Back image is required"),
    otherwise: () => yup.string().optional()
  })
},
  [
    ["side", "back"],
    ["front", 'back'],
    ["front", "side"],
  ]);