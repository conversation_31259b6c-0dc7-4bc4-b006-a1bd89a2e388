import {StyleSheet} from 'react-native';
import {Fonts, sizes, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    flex: 1,
    paddingTop: sizes.paddingTop + 16,
  },
  calLeftText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    lineHeight: 19,
    color: theme.lightColors?.black,
  },
  calProgess: {
    fontFamily: Fonts.semiBold,
    fontSize: 20,
    color: theme.lightColors?.black,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  smallText: {
    fontFamily: Fonts.regular,
    fontSize: 12,
    color: theme.lightColors?.black,
  },
  arrowIcon: {
    height: 20,
    width: 20,
  },
  dateText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.black,
    textTransform: 'capitalize',
  },
  calNumber: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    marginTop: 5,
    color: theme.lightColors?.black,
  },
  modalHeading: {
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  saveButton: {
    width: '90%',
    marginLeft: 13,
  },
  unitContainer: {
    marginRight: 12,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  uploadContainer: {
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    width: '100%',
    height: 127,
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 10,
  },
  uploadText: {
    fontFamily: Fonts.regular,
    color: theme.lightColors?.secondary,
    marginTop: 24,
  },
  units: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  glassContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingHorizontal: 13,
    backgroundColor: theme.lightColors?.background,
  },
  selectedImage: {
    width: '100%',
    height: 127,
    borderRadius: 14,
  },
  glassIcon: {
    height: 30,
    width: 30,
    backgroundColor: theme.lightColors?.background,
  },
  literText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.black,
    backgroundColor: theme.lightColors?.background,
  },
  waterContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    padding: 10,
    marginTop: 14,
    backgroundColor: theme.lightColors?.background,
  },
  waterTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.lightColors?.background,
  },
  waterDrop: {
    width: 24,
    height: 24,
    marginRight: 1,
    backgroundColor: theme.lightColors?.background,
  },
  stepsContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 13,
    paddingTop: 13,
    paddingHorizontal: 10,
    paddingBottom: 18,
    width: '48%',
    height: 101,
  },
  stepTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepActivity: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 14,
  },
  stepIcon: {height: 24, width: 24, marginRight: 8, tintColor: '#EA1414'},
  stepText: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  stepNumber: {
    color: theme.lightColors?.black,
    textAlign: 'center',
    marginTop: 12,
    fontFamily: Fonts.regular,
    fontSize: 24,
  },
  deleteFood: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  deleteFoodContainer: {paddingVertical: 11, paddingHorizontal: 27},
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  waterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.lightColors?.background,
  },
  waterText: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: 'black',
    backgroundColor: theme.lightColors?.background,
  },
  dailyIntakeGoal: {justifyContent: 'space-between', marginTop: 20},
  forkKnife: {height: 16, width: 16},
  circularProgressContainer: {
    alignItems: 'center',
    marginTop: 16,
    marginRight: 20,
  },
  activityContainer: {marginTop: 7, alignSelf: 'center'},
  timeIcon: {
    width: 16,
    height: 16,
    marginRight: 6,
    tintColor: theme.lightColors?.black,
  },
  minText: {
    fontFamily: Fonts.regular,
    color: theme.lightColors?.black,
  },
  coachContainer: {
    marginTop: 80,
  },
  statsText: {
    fontFamily: Fonts.bold,
    color: theme.lightColors?.primary,
    fontSize: 22,
    marginVertical: 20,
    alignSelf: 'center',
  },
  goalContainer: {
    backgroundColor: '#fff1ed',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 24,
    paddingHorizontal: 16,
    borderRadius: 16,
  },
  goalInnerContainer: {marginLeft: 8, width: '60%'},
  goalText: {
    fontFamily: Fonts.bold,
    color: theme.lightColors?.primary,
    fontSize: 16,
  },
  questionText: {
    fontFamily: Fonts.bold,
    color: theme.lightColors?.black,
    fontSize: 14,
  },
  chatButton: {marginTop: 16, width: 120},
  noGoalImage: {width: 110, height: 120},
  imageContainer: {flex: 1, flexDirection: 'row', flexWrap: 'wrap'},
  imageWidth: {width: '50%', paddingRight: 10},
  uploadIcon: {width: 44, height: 47},
  sideImage: {width: '50%', paddingLeft: 10},
  calendarContainer: {justifyContent: 'space-between', marginTop: 31},
});
