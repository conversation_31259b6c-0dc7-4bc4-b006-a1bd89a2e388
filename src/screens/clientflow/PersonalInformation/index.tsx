import {
  Keyboard,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {
  Avatar,
  Button,
  FormInput,
  SelectOptionModal,
} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {useFormik} from 'formik';
import {useAppDispatch} from '../../../store';
import * as Yup from 'yup';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import ClientAuthServices from '../../../services/ClientAuthServices';
import {updateUser} from '../../../store/userSlice';
import {useTranslation} from 'react-i18next';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import useClients from '../../../hooks/models/useClients';
import CustomBottomSheet, {
  CustomBottomSheetRef,
} from '../../../components/BottomSheet/CustomBottomSheet';
import BottomSheet from '@gorhom/bottom-sheet';
import HeightEditorModal from './HeightEditor';
import HeightEditor from './HeightEditor';
import WeightEditor from './WeightEditor';

type Props = NativeStackScreenProps<HomeStackParamList, 'PersonalInfo'>;

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  dob: Yup.string().required('Date of birth is required'),
  profilePicture: Yup.string(),
  gender: Yup.string().required(),
  height: Yup.string().required(),
  currentWeight: Yup.string().required(),
  goal: Yup.string().required(),
  goalWeight: Yup.string().required('Goal weight is required'),
  activeBasis: Yup.string().required(),
});
const Services = new AuthServices();

const ClientPersonalInfo: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);

  const {client} = useClients();
  const [imageDetail, setImageDetail] = useState<ImageOrVideo>();
  const [isLoading, setIsLoading] = useState(false);
  const heightBottomSheetRef = React.useRef<CustomBottomSheetRef>(null);
  const weightBottomSheetRef = React.useRef<CustomBottomSheetRef>(null);
  const dispatch = useAppDispatch();

  const handleChangeImage = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        compressImageQuality: 0.5,
        mediaType: 'photo',
      });

      formik.setFieldValue('profilePicture', image.path);
      setImageDetail(image);
    } catch (error) {
      console.log('Error selecting image', error);
    }
  };

  const handleUploadImage = async () => {
    if (imageDetail) {
      try {
        const formData = new FormData();
        formData.append('file', {
          uri: imageDetail?.path,
          type: imageDetail?.mime,
          name: client._id,
        });

        const resp = await ClientAuthServices.ClientProfilePicture(formData);
        if (resp.status == 201) {
          return resp.data;
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log('Error uploading image', err.request);
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Error while uploading picture'),
        });
      }
    }
  };
  const handleSaveChanges = async () => {
    try {
      setIsLoading(true);
      const uploadedImage = await handleUploadImage();
      dispatch(
        updateUser({
          name: formik.values.name,
          email: formik.values.email,
          dob: formik.values.dob,
          image: uploadedImage?.imageUrl
            ? uploadedImage?.imageUrl
            : client.image,
          gender: formik.values.gender,
          height: formik.values.height,
          goal: formik.values.goal,
          goalWeight: formik.values.goalWeight,
          howActive: formik.values.activeBasis,
        }),
      );
      navigation.goBack();

      await Services.UpdateClient(
        {
          name: formik.values.name,
          email: formik.values.email,
          dob: formik.values.dob,
          gender: formik.values.gender,
          height: formik.values.height,
          goal: formik.values.goal,
          goalWeight: formik.values.goalWeight,
          howActive: formik.values.activeBasis,
        },
        client._id,
      );
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error updating data', err.response?.data);
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Error saving your personal information'),
      });
    } finally {
      setIsLoading(false);
    }
  };
  function handleSaveHeight(height: string) {
    formik.setFieldValue('height', height);
    updateUser({
      height: formik.values.height,
    });
    heightBottomSheetRef.current?.close();
  }
  function handleSaveWeight(weight: string) {
    formik.setFieldValue('currentWeight', weight);
    updateUser({
      currentWeight: formik.values.currentWeight,
    });
    weightBottomSheetRef.current?.close();
  }

  const formik = useFormik({
    initialValues: {
      name: client.name,
      email: client.email,
      dob: client.dob,
      profilePicture: client.image,
      gender: client.gender,
      height: client.height,
      currentWeight: client.weight,
      goal: client?.goal,
      goalWeight: client?.goalWeight,
      activeBasis: client.howActive,
    },
    validateOnMount: true,
    validationSchema: validationSchema,
    onSubmit: handleSaveChanges,
  });

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 30}}>
        <View style={{marginTop: 31}}>
          <Avatar
            uri={formik.values.profilePicture}
            onPress={handleChangeImage}
            showPickImage
          />
        </View>
        <Text style={[styles.headingText, {marginTop: 48, color: 'black'}]}>
          {t('Account')}
        </Text>
        <FormInput
          label={t('Name')}
          editIcon
          value={formik.values.name}
          onChangeText={formik.handleChange('name')}
          onBlur={formik.handleBlur('name')}
          errorMessage={
            formik.touched.name ? t(formik.errors.name as string) : undefined
          }
        />
        <FormInput
          label={t('Email')}
          value={formik.values.email}
          onChangeText={formik.handleChange('email')}
          errorMessage={
            formik.touched.email ? t(formik.errors.email as string) : undefined
          }
          disabled
        />
        <Text style={[styles.headingText, {color: 'black'}]}>Health</Text>

        <Text
          style={{
            color: theme.lightColors?.black,
            fontFamily: Fonts.light,
            marginBottom: 8,
          }}>
          {t('Date of birth')}
        </Text>
        <TouchableOpacity
          style={styles.datePicker}
          onPress={() => setIsDatePickerVisible(true)}>
          <Text
            style={{color: theme.lightColors?.black, fontFamily: Fonts.medium}}>
            {!formik.values?.dob
              ? t('Pick Date')
              : moment(formik.values?.dob).format('MMMM Do, YYYY')}
          </Text>
        </TouchableOpacity>

        <SelectOptionModal
          label={t('Gender')}
          optionArray={['male', 'female']}
          value={formik.values.gender as string}
          setValue={formik.handleChange('gender')}
        />
        <Pressable
          onPress={() => {
            heightBottomSheetRef.current?.open();
          }}>
          <FormInput
            label={t('Height')}
            value={formik.values.height}
            onChangeText={formik.handleChange('height')}
            keyboardType="number-pad"
            disabled
            disabledInputStyle={styles.disabledCustomTextStyle}
            errorMessage={
              formik.touched.height
                ? t(formik.errors.height as string)
                : undefined
            }
            editIcon
            onRightIconPress={() => {
              heightBottomSheetRef.current?.open();
            }}
          />
        </Pressable>
        <Pressable
          onPress={() => {
            weightBottomSheetRef.current?.open();
          }}>
          <FormInput
            label={t('Current Weight')}
            value={formik.values.currentWeight}
            onChangeText={formik.handleChange('currentWeight')}
            keyboardType="number-pad"
            errorMessage={
              formik.touched.currentWeight
                ? t(formik.errors.currentWeight as string)
                : undefined
            }
            editIcon
            disabled
            disabledInputStyle={styles.disabledCustomTextStyle}
            onRightIconPress={() => {
              weightBottomSheetRef.current?.open();
            }}
          />
        </Pressable>

        <SelectOptionModal
          label={t('Goal')}
          optionArray={['Lose Weight', 'Maintain Weight', 'Gain Weight']}
          value={formik.values.goal as string}
          setValue={formik.handleChange('goal')}
        />
        <FormInput
          label={t('Goal Weight')}
          value={formik.values.goalWeight}
          onChangeText={formik.handleChange('goalWeight')}
          keyboardType="number-pad"
          errorMessage={
            formik.touched.goalWeight
              ? t(formik.errors.goalWeight as string)
              : undefined
          }
        />

        <SelectOptionModal
          label={t('Active Basis')}
          optionArray={[
            'Sedentary',
            'Lightly Active',
            'Moderately Active',
            'Highly Active',
          ]}
          value={formik.values.activeBasis as string}
          setValue={formik.handleChange('activeBasis')}
        />

        <Button
          title={t('Save Changes')}
          onPress={formik.handleSubmit}
          loading={isLoading}
          disabled={isLoading || !formik.isValid}
        />
      </ScrollView>

      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        date={
          formik.values.dob ? moment(formik.values.dob).toDate() : new Date()
        }
        maximumDate={new Date()}
        mode="date"
        onConfirm={date => {
          formik.setFieldValue('dob', moment(date).format('YYYY-MM-DD'));
          setIsDatePickerVisible(false);
        }}
        onCancel={() => setIsDatePickerVisible(false)}
      />

      <CustomBottomSheet ref={heightBottomSheetRef}>
        <HeightEditor
          initialHeight={formik.values.height}
          onSave={handleSaveHeight}
          onCancel={() => {
            heightBottomSheetRef.current?.close();
          }}
          isLoading={isLoading}
        />
      </CustomBottomSheet>
      <CustomBottomSheet
        customSnapPoints={['75%', '90%']}
        ref={weightBottomSheetRef}>
        <WeightEditor
          initialWeight={formik.values.currentWeight}
          onSave={handleSaveWeight}
          onCancel={() => {
            weightBottomSheetRef.current?.close();
          }}
          isLoading={isLoading}
        />
      </CustomBottomSheet>
    </View>
  );
};

export default ClientPersonalInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  headingText: {
    fontFamily: Fonts.bold,
    marginBottom: 10,
    fontSize: 16,
  },
  datePicker: {
    backgroundColor: theme.lightColors?.background,
    borderColor: theme.lightColors?.grey1,
    borderWidth: 1,
    borderRadius: 10,
    padding: 14,
    marginBottom: 24,
  },
  disabledCustomTextStyle: {
    color: theme.lightColors?.black,
    opacity: 1.0,
  },
});
