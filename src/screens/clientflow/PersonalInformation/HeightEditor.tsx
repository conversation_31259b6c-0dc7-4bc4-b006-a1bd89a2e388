import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';
import {FormInput, Button} from '../../../components';
import {useTranslation} from 'react-i18next';

interface HeightEditorProps {
  initialHeight?: string; // Format: "5.10 ft/in" or "180 meter"
  onSave: (height: string) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const HeightEditor: React.FC<HeightEditorProps> = ({
  initialHeight,
  onSave,
  onCancel,
  isLoading = false,
}) => {
  const [unit, setUnit] = useState<'ft/in' | 'meter'>('ft/in');
  const [feetValue, setFeetValue] = useState('');
  const [inchValue, setInchValue] = useState('');
  const [rightUnitText, setRightUnitText] = useState("''");
  const [leftUnitText, setLeftUnitText] = useState("'");
  const {t} = useTranslation();

  // Parse initial height value
  useEffect(() => {
    if (initialHeight) {
      const parts = initialHeight.split(' ');
      if (parts.length >= 2) {
        const heightUnit = parts[1] as 'ft/in' | 'meter';
        const heightValue = parts[0];

        setUnit(heightUnit);

        if (heightUnit === 'ft/in') {
          const [feet, inches] = heightValue.split('.');
          setFeetValue(feet || '');
          setInchValue(inches || '');
        } else {
          const [meters, centimeters] = heightValue.split('.');
          setFeetValue(meters || '');
          setInchValue(centimeters || '');
        }
      }
    }
  }, [initialHeight]);

  useEffect(() => {
    setLeftUnitText(unit === 'ft/in' ? "'" : 'm');
    setRightUnitText(unit === 'ft/in' ? "''" : 'cm');
  }, [unit]);

  const handleSave = () => {
    if (feetValue || inchValue) {
      const heightString = `${feetValue}.${inchValue} ${unit}`;
      onSave(heightString);
    }
  };

  const isValid = !!(feetValue || inchValue);

  return (
    <KeyboardAvoidingView style={styles.container}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onCancel}>
            <Text style={styles.cancelText}>{t('Cancel')}</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{t('Edit Height')}</Text>
          <TouchableOpacity
            onPress={handleSave}
            disabled={!isValid || isLoading}>
            <Text
              style={[
                styles.saveText,
                (!isValid || isLoading) && styles.disabledText,
              ]}>
              {t('Save')}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Text style={styles.promptText}>
            {t('What is your current height?')}
          </Text>

          <View style={styles.inputContainer}>
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  value={feetValue}
                  label=""
                  placeholder="0"
                  placeholderTextColor={theme.lightColors?.grey0}
                  onChangeText={setFeetValue}
                  textinputStyles={styles.inputStyle}
                  keyboardType="numeric"
                  returnKeyType="done"
                  inputContainerStyle={styles.inputBox}
                  unitText={leftUnitText}
                />
              </View>
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  value={inchValue}
                  label=""
                  placeholder="0"
                  placeholderTextColor={theme.lightColors?.placeholderColor}
                  onChangeText={setInchValue}
                  textinputStyles={styles.inputStyle}
                  keyboardType="numeric"
                  returnKeyType="done"
                  inputContainerStyle={styles.inputBox}
                  unitText={rightUnitText}
                />
              </View>
            </View>

            <View style={styles.unitSelector}>
              <TouchableOpacity
                style={[
                  styles.unitButton,
                  {
                    backgroundColor:
                      unit === 'ft/in'
                        ? theme.lightColors?.primary
                        : theme.lightColors?.grey1,
                  },
                ]}
                onPress={() => setUnit('ft/in')}>
                <Text
                  style={[
                    styles.unitText,
                    {
                      color:
                        unit === 'ft/in'
                          ? theme.lightColors?.white
                          : theme.lightColors?.grey7,
                    },
                  ]}>
                  ft/in
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.unitButton,
                  {
                    backgroundColor:
                      unit === 'meter'
                        ? theme.lightColors?.primary
                        : theme.lightColors?.grey1,
                  },
                ]}
                onPress={() => setUnit('meter')}>
                <Text
                  style={[
                    styles.unitText,
                    {
                      color:
                        unit === 'meter' ? theme.lightColors?.white : '#969696',
                    },
                  ]}>
                  {t('Meter')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.lightColors?.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.lightColors?.grey1,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.black,
  },
  cancelText: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.grey0,
  },
  saveText: {
    fontSize: 16,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.primary,
    paddingTop: 10,
  },
  disabledText: {
    color: theme.lightColors?.grey1,
  },
  content: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    flex: 1,
    justifyContent: 'space-between',
  },
  promptText: {
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
    textAlign: 'center',
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 30,
    flex: 1,
  },
  goalInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  inputStyle: {
    paddingVertical: 15,
    textAlign: 'center',
  },
  inputBox: {
    backgroundColor: theme.lightColors?.white,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 10,
  },
  unitSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  unitButton: {
    marginHorizontal: 6,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  unitText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
  },
});

export default HeightEditor;
