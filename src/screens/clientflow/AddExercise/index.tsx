import {
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import MealSelect from '../../../components/MealSelect/MealSelect';
import {Fonts, theme} from '../../../utilities/theme';
import {SearchBar} from '../../../components';

const AddExercise = () => {
  const [selectedMeals, setSelectedMeals] = useState<Record<string, boolean>>(
    {},
  );
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const categories = ['All', 'Cardiovascular', 'Strength', 'Light'];

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
  };

  const toggleTickIcon = (mealName: string) => {
    setSelectedMeals(prevState => ({
      ...prevState,
      [mealName]: !prevState[mealName],
    }));
  };
  const SearchResults = [
    {name: 'Aerobic'},
    {name: 'Bicycling'},
    {name: 'Push ups'},
    {name: 'Belly Dancing'},
    {name: 'Bench Press'},
    {name: 'Squats'},
    {name: 'Deadlifts'},
    {name: 'Plank'},
    {name: 'Crunches'},
  ];
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 30}}>
        <SearchBar
          value={searchText}
          micBtn
          onChangeText={v => setSearchText(v)}
          containerStyle={{paddingHorizontal: 24}}
        />
        <FlatList
          data={categories}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryContainer}
          renderItem={({item}) => (
            <TouchableOpacity
              style={[
                selectedCategory === item
                  ? styles.selectedContainer
                  : styles.unselectedContainer,
                {marginRight: 8},
              ]}
              onPress={() => handleCategorySelect(item)}>
              <Text
                style={
                  selectedCategory === item
                    ? styles.selectedText
                    : styles.unselectedText
                }>
                {item}
              </Text>
            </TouchableOpacity>
          )}
        />
        <View style={{paddingHorizontal: 24}}>
          {SearchResults.map((result, index) => (
            <MealSelect
              key={index}
              name={result.name}
              tickIcon={selectedMeals[result.name]}
              onPress={() => toggleTickIcon(result.name)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default AddExercise;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
  },
  categoryContainer: {
    paddingHorizontal: 24,
    paddingTop: 8,
    paddingBottom: 12,
  },
  selectedText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.white,
  },
  selectedContainer: {
    backgroundColor: theme.lightColors?.primary,
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
  },
  unselectedText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.primary,
  },
  unselectedContainer: {
    borderColor: theme.lightColors?.primary,
    borderWidth: 1,
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
  },
});
