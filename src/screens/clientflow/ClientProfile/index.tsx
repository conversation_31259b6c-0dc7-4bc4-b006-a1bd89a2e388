import {SafeAreaView, Text, View} from 'react-native';
import React, {useState} from 'react';
import {Avatar} from '../../../components';
import {theme} from '../../../utilities/theme';
import {
  About,
  Coach,
  Goal,
  Logout,
  WeightProgress,
} from '../../../assets/svgIcons';
import {Divider} from '@rneui/base';
import {
  Lock,
  Subscribtion,
  Profile_,
  Documents,
  Language,
  Terms,
  Help,
} from '../../../assets/svgIcons/ProfileActionIcons';
import {ScrollView} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {useDispatch} from 'react-redux';
import {resetUser} from '../../../store/userSlice';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import ProfileAction from '../../homeflow/Profile/ProfileAction';
import {styles} from './styles';
import {ClientBottomStackParamList} from '../../../navigation/ClientBottomNavigation/ClientBottomNavigation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ImagePicker from 'react-native-image-crop-picker';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import moment from 'moment';
import useClients from '../../../hooks/models/useClients';

type Props = NativeStackScreenProps<ClientBottomStackParamList, 'Profile'>;
const Services = new AuthServices();

const ClientProfile: React.FC<Props> = ({navigation}) => {
  const {client} = useClients();
  const {t} = useTranslation();

  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(client.image);
  const dispatch = useDispatch();
  const handleLogout = async () => {
    try {
      dispatch(resetUser());
      setModalVisible(false);
      await AsyncStorage.removeItem('userData');
      await AsyncStorage.removeItem('access_token');
    } catch (error) {
      console.log(error);
    }
  };

  const handleImagePress = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: 'photo',
      });
      setSelectedImage(image.path);
      const formData = new FormData();
      formData.append('file', {
        uri: image.path,
        type: image.mime,
        name: client._id,
      });

      const resp = await Services.UploadPicture(formData);
      if (resp.status == 201) {
        return resp.data.imageUrl;
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error selecting image', err);
    }
  };

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        {/* AVATAR */}
        <Avatar
          uri={client.image}
          onPress={handleImagePress}
          showPickImage={true}
        />

        <Text style={styles.name}>{client.name}</Text>

        <Divider style={styles.divider} />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: 16}}>
          {client.dob ? (
            <View style={styles.infoContainer}>
              <Text style={styles.infoHead}>{t('Age')}</Text>
              <Text style={styles.infoDesc}>
                {moment().diff(moment(client.dob), 'years')} {t('years')}
              </Text>
            </View>
          ) : null}
          <View style={[styles.infoContainer, {marginTop: 12}]}>
            <Text style={styles.infoHead}>{t('Current Weight')}</Text>
            <Text style={styles.infoDesc}>{client.weight}</Text>
          </View>
          <View style={[styles.infoContainer, {marginTop: 12}]}>
            <Text style={styles.infoHead}>{t('Goal')}</Text>
            <Text style={styles.infoDesc}>{t(client?.goal as string)}</Text>
          </View>
          <Divider style={styles.divider} />

          {/* PROFILE ACTION */}
          <ProfileAction
            icon={<Profile_ />}
            label={t('Personal Information')}
            title={t('General')}
            marginTop={0.1}
            onPress={() => navigation.navigate('PersonalInfo')}
          />
          <ProfileAction
            icon={<Goal />}
            label={t('My Goals')}
            onPress={() => navigation.navigate('MyGoals')}
          />
          {/* <ProfileAction
            icon={<Subscribtion />}
            label={t('Subscription')}
            onPress={() =>
              navigation.navigate('ViewSubscription', {isProfile: true})
            }
          /> */}
          <ProfileAction
            icon={<WeightProgress />}
            label={t('Weight Progress')}
            onPress={() =>
              navigation.navigate('WeightProgress', {isClient: true})
            }
          />
          <ProfileAction
            icon={<Coach />}
            label={t('Coach')}
            onPress={() => navigation.navigate('CoachProfile')}
          />
          <ProfileAction
            icon={<Documents />}
            label={t('Document')}
            onPress={() => navigation.navigate('Document', {isClient: true})}
          />
          <ProfileAction
            icon={<Lock />}
            label={t('Change Password')}
            onPress={() => navigation.navigate('PreviousPassword')}
          />
          <ProfileAction
            icon={<Language />}
            label={t('Language')}
            onPress={() => navigation.navigate('Language2', {isClient: true})}
          />
          <ProfileAction
            icon={<Terms />}
            label={t('Terms & Conditions')}
            title={t('Help & Support')}
            onPress={() => navigation.navigate('TermsAndCondition')}
          />
          <ProfileAction
            icon={<Help />}
            label={t('Help Center')}
            onPress={() => navigation.navigate('HelpCenter')}
          />
          <ProfileAction
            icon={
              <About
                width={28}
                height={28}
                stroke={theme.lightColors?.primary}
              />
            }
            label={t('About')}
            onPress={() => navigation.navigate('ClientAbout')}
          />
          <ProfileAction
            icon={
              <Logout
                width={28}
                height={28}
                stroke={theme.lightColors?.primary}
              />
            }
            label={t('Log Out')}
            title={t('Log Out')}
            onPress={() => setModalVisible(true)}
          />
        </ScrollView>
      </View>
      <GeneralModal
        visible={isModalVisible}
        onCancel={() => setModalVisible(false)}
        primaryButtonOnpress={handleLogout}
        topRedTitle={t('Log Out')}
        description={t('You are attempting to log out.')}
        primaryButtonName={t('Yes, Log Out')}
        secondaryButtonText={t('Cancel')}
      />
    </SafeAreaView>
  );
};

export default ClientProfile;
