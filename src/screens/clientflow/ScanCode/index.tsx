import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import QRCodeScanner from 'react-native-qrcode-scanner';
import PNGIcons from '../../../assets/pngIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {IFoodMeal, IMealDetails} from '../../../interfaces/IMeal';
import {IFood} from '../../../interfaces/IFoods';
import {useAppDispatch} from '../../../store';
import {updateMeal} from '../../../store/MealsSlice';
import {AxiosError} from 'axios';
import {addNewFood} from '../../../store/FoodsSlice';
import FoodServices from '../../../services/FoodServices';
import useClients from '../../../hooks/models/useClients';
import MealServices from '../../../services/MealServices';
import {theme} from '../../../utilities/theme';
import useApiHandler from '../../../utilities/useApiHandler';

type Props = NativeStackScreenProps<ClientStackParamList, 'ScanCode'>;

const ScanCode: React.FC<Props> = ({navigation, route}) => {
  const {handleAxiosErrors} = useApiHandler();

  const [mealDetails, setMealDetails] = useState<IMealDetails>();
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const {fetchClient} = useClients();
  const dispatch = useAppDispatch();

  const {mealId} = route.params;
  useEffect(() => {
    getMealDetails();
  }, [mealId]);

  async function getMealDetails() {
    try {
      const resp = await MealServices.GetMealDetails(mealId as string);

      if (resp.status === 200) {
        const mealData = resp.data as IMealDetails;
        setMealDetails(mealData as IMealDetails);
      }
    } catch (error) {
      console.log('Error getting meal details:', error);
    } finally {
      setIsFetching(false);
    }
  }

  const onSuccess = async (e: any) => {
    setIsLoading(true);
    const barcode = e.data;
    const apiUrl = `https://world.openfoodfacts.org/api/v2/product/${barcode}.json`;

    try {
      const response = await fetch(apiUrl);
      if (!response.ok)
        throw new Error(`HTTP error! Status: ${response.status}`);

      const data = await response.json();
      const calories =
        (200 / 100) * data.product.nutriments['energy-kcal_100g'];

      const payload = {
        name: data.product.product_name,
        quantity: 2,
        calories: calories,
        proteins: data.product.nutriments.proteins,
        carbs: data.product.nutriments.carbohydrates,
        fats: data.product.nutriments.fat,
      };

      const resp = await FoodServices.CreateFood(payload);
      if (resp.status === 201) {
        const newFood = resp.data;
        const filteredFoods =
          mealDetails?.mealFoods.map(item => ({
            foodId: item._id,
            serving: '1',
          })) || [];

        addFoodToMeal([...filteredFoods, newFood]);
        dispatch(addNewFood(newFood as IFood));
      }
    } catch (error) {
      console.log('Error fetching product information:', error);
      setIsLoading(false);
    }
  };

  async function addFoodToMeal(foodsArr: IFood[]) {
    const mealFoodsPayload = foodsArr.map(item => ({
      foodId: item._id,
      serving: '1',
    })) as IFoodMeal[];

    try {
      const resp = await MealServices.UpdateMeal(mealId as string, {
        mealTitle: mealDetails?.mealTitle,
        mealFoods: mealFoodsPayload,
      });

      if (resp.status === 200) {
        dispatch(updateMeal(resp.data));
        fetchClient();
        navigation.goBack();
      }
    } catch (error) {
      console.log('Error updating meal:', error);
      const err = error as AxiosError;
      handleAxiosErrors(err);
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <View style={styles.container}>
      {isFetching ? (
        <ActivityIndicator
          color={theme.lightColors?.primary}
          size={'large'}
          style={{alignSelf: 'center', marginTop: 600}}
        />
      ) : (
        <QRCodeScanner
          onRead={onSuccess}
          showMarker
          vibrate
          // reactivate
          topContent={
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: 20,
                right: 30,
              }}
              onPress={() => navigation.goBack()}>
              <Image
                source={PNGIcons.CrossIcon}
                style={{height: 24, width: 24}}
              />
            </TouchableOpacity>
          }
          fadeIn={isLoading}
          bottomContent={
            isLoading ? (
              <ActivityIndicator
                color={theme.lightColors?.primary}
                size={'large'}
                style={{alignSelf: 'center', marginTop: -600}}
              />
            ) : undefined
          }
        />
      )}
    </View>
  );
};

export default ScanCode;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.white,
  },
});
