import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {IChat} from '../interfaces/IChat';
import {IQueryResponse} from '../interfaces/QueryResponse';
import {GiftedChat, IMessage} from 'react-native-gifted-chat';

interface IChatInitialeState {
  chats: IQueryResponse<IChat>;
  unreadCounter: number;
  socketId?: string;
  chatMessages: IMessage[];
}

const initialState: IChatInitialeState = {
  chats: {count: 0, results: []},
  unreadCounter: 0,
  socketId: '',
  chatMessages: [],
};

const ChatsSlice = createSlice({
  name: 'Chats',
  initialState,
  reducers: {
    setChats: (state, {payload}) => {
      state.chats = payload;
      return state;
    },
    setChatMessages: (state, {payload}: PayloadAction<IMessage[]>) => {
      state.chatMessages = payload;
      return state;
    },
    appendMessages: (state, {payload}: PayloadAction<IMessage[]>) => {
      state.chatMessages = GiftedChat.append(state.chatMessages, payload);
      return state;
    },
    updateUnreadCounter: (
      state,
      {payload}: PayloadAction<{counter: number}>,
    ) => {
      state.unreadCounter = payload.counter;
      return state;
    },
    updateChat: (state, {payload}: PayloadAction<{chat: Partial<IChat>}>) => {
      const chatIndex = state.chats.results.findIndex(
        chat => chat._id === payload.chat._id,
      );
      if (chatIndex !== -1) {
        state.chats.results[chatIndex] = {
          ...state.chats.results[chatIndex],
          ...payload,
        };
      }
      return state;
    },
    setSocketId: (state, {payload}: PayloadAction<{socketId?: string}>) => {
      state.socketId = payload.socketId;
      return state;
    },
    resetChats: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {
  setChats,
  updateUnreadCounter,
  updateChat,
  setSocketId,
  setChatMessages,
  appendMessages,
  resetChats,
} = ChatsSlice.actions;

// REDUCER
export default ChatsSlice;
