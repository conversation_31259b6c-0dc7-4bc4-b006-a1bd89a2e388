import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IFood} from '../interfaces/IFoods';

interface IFoodInitialState extends Array<IFood> {}

const initialState: IFoodInitialState = [];

// SLICE
export const FoodsSlice = createSlice({
  name: 'foods',
  initialState,
  reducers: {
    addNewFood: (state, {payload}: PayloadAction<IFood>) => {
      state = [...state, payload];
      return state;
    },
    setFoods: (state, {payload}: PayloadAction<Array<IFood>>) => {
      state = payload;
      return state;
    },
    updateFood: (state, {payload}: PayloadAction<Partial<IFood>>) => {
      let index = state.findIndex(item => item._id === payload._id);
      state[index] = {...state[index], ...payload};
      return state;
    },
    removeFood: (state, {payload}: PayloadAction<Partial<IFood>>) => {
      state = state.filter(item => item._id !== payload._id);
      return state;
    },
    resetFoods: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {setFoods, updateFood, removeFood, addNewFood, resetFoods} =
  FoodsSlice.actions;

// REDUCER
export default FoodsSlice;
