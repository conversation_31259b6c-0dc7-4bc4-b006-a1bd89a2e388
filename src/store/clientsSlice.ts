import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IUser} from '../interfaces/IUser.';

interface IClientsInitialState {
  clients: IUser[];
  count: number;
  isLoading: boolean;
  archivedClients: IUser[];
}

const initialState: IClientsInitialState = {
  clients: [],
  count: 0,
  isLoading: true,
  archivedClients: [],
};

// SLICE
export const ClientsSlice = createSlice({
  name: 'clients',
  initialState,
  reducers: {
    setClients: (
      state,
      {payload}: PayloadAction<{clients: IUser[]; count: number}>,
    ) => {
      state.clients = payload.clients;
      state.count = payload.count;
      return state;
    },
    updateClient: (state, {payload}: PayloadAction<Partial<IUser>>) => {
      let index = state.clients.findIndex(item => item._id === payload._id);
      state.clients[index] = {...state.clients[index], ...payload};
      return state;
    },
    deleteClient: (state, {payload}: PayloadAction<{_id: string}>) => {
      state.clients = state.clients.filter(item => item._id !== payload._id);
      state.archivedClients = state.archivedClients.filter(
        item => item._id !== payload._id,
      );
      state.count = state.clients.length;
      return state;
    },
    updateLoadingStatus: (state, {payload}: PayloadAction<boolean>) => {
      state.isLoading = payload;
      return state;
    },
    setArchivedClients: (
      state,
      {payload}: PayloadAction<{archivedclients: IUser[]}>,
    ) => {
      state.archivedClients = payload.archivedclients;
      return state;
    },
    toggleArchiveClient: (state, {payload}: PayloadAction<{_id: string}>) => {
      const clientIndex = state.clients.findIndex(
        item => item._id === payload._id,
      );
      if (clientIndex !== -1) {
        const [client] = state.clients.splice(clientIndex, 1);
        client.archived = true;
        state.archivedClients.push(client);
      } else {
        const archivedClientIndex = state.archivedClients.findIndex(
          item => item._id === payload._id,
        );
        if (archivedClientIndex !== -1) {
          const [archivedClient] = state.archivedClients.splice(
            archivedClientIndex,
            1,
          );
          archivedClient.archived = false;
          state.clients.push(archivedClient);
        }
      }
      state.count = state.clients.length;
      return state;
    },
    resetClients: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {
  setClients,
  updateClient,
  updateLoadingStatus,
  deleteClient,
  setArchivedClients,
  toggleArchiveClient,
  resetClients,
} = ClientsSlice.actions;

// REDUCER
export default ClientsSlice;
