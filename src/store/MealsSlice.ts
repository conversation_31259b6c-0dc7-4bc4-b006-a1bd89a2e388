import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IMeal} from '../interfaces/IMeal';
import {IFood} from '../interfaces/IFoods';

interface IMealInitialState {
  meals: IMeal[];
  mealFoods: IFood[];
}

const initialState: IMealInitialState = {
  meals: [],
  mealFoods: [],
};

// SLICE
export const MealsSlice = createSlice({
  name: 'meals',
  initialState,
  reducers: {
    setDailyMeals: (state, {payload}: PayloadAction<Array<IMeal>>) => {
      state.meals = payload;
      return state;
    },
    addNewMeal: (state, {payload}: PayloadAction<IMeal>) => {
      state.meals = [...state.meals, payload];
      return state;
    },
    updateMeal: (state, {payload}: PayloadAction<Partial<IMeal>>) => {
      let index = state.meals.findIndex(item => item._id === payload._id);
      state.meals[index] = {...state.meals[index], ...payload};
      return state;
    },
    removeMeal: (state, {payload}: PayloadAction<Partial<IMeal>>) => {
      state.meals = state.meals.filter(item => item._id !== payload._id);
      return state;
    },
    setMealFoods: (state, {payload}: PayloadAction<IFood[]>) => {
      state.mealFoods = payload;
      return state;
    },
    resetMeals: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {
  setDailyMeals,
  updateMeal,
  removeMeal,
  addNewMeal,
  setMealFoods,
  resetMeals,
} = MealsSlice.actions;

// REDUCER
export default MealsSlice;
