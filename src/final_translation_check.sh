#!/bin/bash

# Final comprehensive translation check
echo "=== FINAL TRANSLATION VERIFICATION ==="
echo "======================================"

# Create output files
FINAL_MISSING="src/final_missing_keys.txt"
FINAL_REPORT="src/final_translation_report.txt"

# Clear previous results
> "$FINAL_MISSING"
> "$FINAL_REPORT"

echo "Step 1: Extracting all t() function calls with improved regex..."

# Extract t() calls more precisely
find . -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.jsx" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -ho "t(['\"][^'\"]*['\"])" | \
sed "s/t(['\"]//g; s/['\"])//g" | \
grep -v "^$" | \
grep -v "^\.$" | \
sort | uniq > temp_all_calls.txt

echo "Step 2: Checking each key against English translation file..."

missing_count=0
while IFS= read -r key; do
    # Trim whitespace and check if key is not empty and not just a dot
    trimmed_key=$(echo "$key" | xargs)
    if [ ! -z "$trimmed_key" ] && [ "$trimmed_key" != "." ]; then
        # Use grep with fixed strings to avoid regex issues
        if ! grep -Fq "\"$trimmed_key\":" src/i18n/locales/en.json; then
            echo "$trimmed_key" >> "$FINAL_MISSING"
            ((missing_count++))
        fi
    fi
done < temp_all_calls.txt

echo "Step 3: Generating final report..."

{
    echo "=== FINAL TRANSLATION VERIFICATION REPORT ==="
    echo "Generated on: $(date)"
    echo ""
    echo "SUMMARY:"
    echo "--------"
    echo "Total valid t() function calls found: $(grep -v '^$\|^\.$' temp_all_calls.txt | wc -l)"
    echo "Missing translation keys: $missing_count"
    echo ""
    if [ $missing_count -gt 0 ]; then
        echo "MISSING KEYS:"
        echo "-------------"
        cat "$FINAL_MISSING"
    else
        echo "🎉 ALL TRANSLATION KEYS ARE PRESENT! 🎉"
        echo "No missing translation keys found."
    fi
    echo ""
    echo "VERIFICATION STATUS:"
    echo "-------------------"
    if [ $missing_count -eq 0 ]; then
        echo "✅ TRANSLATION COVERAGE: 100% COMPLETE"
    else
        echo "❌ TRANSLATION COVERAGE: INCOMPLETE ($missing_count missing keys)"
    fi
} > "$FINAL_REPORT"

# Clean up
rm -f temp_all_calls.txt

echo ""
echo "Final verification complete!"
echo "Results saved to:"
echo "  - Missing keys: $FINAL_MISSING"
echo "  - Final report: $FINAL_REPORT"
echo ""
if [ $missing_count -eq 0 ]; then
    echo "🎉 SUCCESS: All translation keys are present!"
else
    echo "❌ INCOMPLETE: $missing_count missing translation keys found"
fi
