export const USERCHATS = [
  {
    id: 1,
    name: '<PERSON>',
    avatarUrl: 'https://example.com/avatar1.png',
    message: 'Hello world!',
    time: '10:00 AM',
    check: true,
    doubleCheck: false,
    messageCounter: false,
  },
  {
    id: 2,
    name: '<PERSON>',
    avatarUrl: 'https://example.com/avatar2.png',
    message: 'Hey there!',
    time: '10:05 AM',
    check: false,
    doubleCheck: true,
    messageCounter: false,
  },
  {
    id: 3,
    name: '<PERSON>',
    avatarUrl: 'https://example.com/avatar3.png',
    message: 'How are you?',
    time: '10:10 AM',
    check: false,
    doubleCheck: false,
    messageCounter: true,
  },
  {
    id: 4,
    name: '<PERSON>',
    avatarUrl: 'https://example.com/avatar4.png',
    message: "I'm doing great, thanks!",
    time: '10:15 AM',
    check: false,
    doubleCheck: true,
    messageCounter: false,
  },
  {
    id: 5,
    name: '<PERSON>',
    avatarUrl: 'https://example.com/avatar5.png',
    message: "What's up?",
    time: '10:20 AM',
    check: true,
    doubleCheck: false,
    messageCounter: true,
  },
  {
    id: 6,
    name: '<PERSON>',
    avatarUrl: 'https://example.com/avatar6.png',
    message: 'Not much, just chilling.',
    time: '10:25 AM',
    check: false,
    doubleCheck: false,
    messageCounter: true,
  },
  {
    id: 7,
    name: 'Michael',
    avatarUrl: 'https://example.com/avatar7.png',
    message: 'Anyone want to grab lunch?',
    time: '10:30 AM',
    check: true,
    doubleCheck: false,
    messageCounter: true,
  },
  {
    id: 8,
    name: 'Emma',
    avatarUrl: 'https://example.com/avatar8.png',
    message: "Sure, I'm in!",
    time: '10:35 AM',
    check: false,
    doubleCheck: true,
    messageCounter: false,
  },
  {
    id: 9,
    name: 'Daniel',
    avatarUrl: 'https://example.com/avatar9.png',
    message: 'Me too!',
    time: '10:40 AM',
    check: true,
    doubleCheck: false,
    messageCounter: false,
  },
  {
    id: 10,
    name: 'Olivia',
    avatarUrl: 'https://example.com/avatar10.png',
    message: 'Count me in as well.',
    time: '10:45 AM',
    check: false,
    doubleCheck: false,
    messageCounter: true,
  },
];
