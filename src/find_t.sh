#!/bin/bash

# Script to find t() function calls in files
# Usage: ./find_t_function.sh [directory_path]

# Set directory to search (default to current directory if not provided)
SEARCH_DIR="${1:-.}"

# Check if directory exists
if [ ! -d "$SEARCH_DIR" ]; then
    echo "Error: Directory '$SEARCH_DIR' does not exist."
    exit 1
fi

echo "Searching for t() function calls in: $SEARCH_DIR"
echo "----------------------------------------"

# Find all files and search for t() function
find "$SEARCH_DIR" -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" -o -name "*.vue" -o -name "*.php" -o -name "*.py" \) -exec grep -Hn "t(" {} \; | while IFS=: read -r filepath linenum linetext; do
    echo "File: $filepath"
    echo "Line: $linenum"
    echo "Text: $linetext"
    echo "----------------------------------------"
done

# Alternative version that searches all text files
# Uncomment the following lines and comment the above section if you want to search all text files

# find "$SEARCH_DIR" -type f -exec file {} \; | grep -i text | cut -d: -f1 | while read -r filepath; do
#     grep -Hn "t(" "$filepath" 2>/dev/null | while IFS=: read -r linenum linetext; do
#         echo "File: $filepath"
#         echo "Line: $linenum"
#         echo "Text: $linetext"
#         echo "----------------------------------------"
#     done
# done
