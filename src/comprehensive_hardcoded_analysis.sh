#!/bin/bash

# Comprehensive analysis of hard-coded strings that need t() wrapper
echo "=== COMPREHENSIVE HARD-CODED STRINGS ANALYSIS ==="
echo "================================================"

# Create output files
HARDCODED_REPORT="src/comprehensive_hardcoded_report.txt"
HARDCODED_STRINGS="src/comprehensive_hardcoded_strings.txt"
PRIORITY_FIXES="src/priority_hardcoded_fixes.txt"

# Clear previous results
> "$HARDCODED_REPORT"
> "$HARDCODED_STRINGS"
> "$PRIORITY_FIXES"

echo "Step 1: Analyzing JSX text content..."

# Find JSX text content that should be translated
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E ">\s*[A-Z][^<>]*<" | \
grep -v "t(" | \
grep -v "console\." | \
grep -v "// " | \
head -30 > temp_jsx_text.txt

echo "Step 2: Finding string literals in components..."

# Find string literals that might need translation
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "(title|label|placeholder|text|message|error|success|warning|info).*=.*['\"][A-Z][^'\"]*['\"]" | \
grep -v "t(" | \
head -30 > temp_string_literals.txt

echo "Step 3: Finding validation error messages..."

# Find validation error messages not using t()
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "\.(required|min|max|matches|email|oneOf)\(['\"][^'\"]*['\"]" | \
grep -v "t(" | \
head -20 > temp_validation_errors.txt

echo "Step 4: Finding toast/alert messages..."

# Find toast and alert messages
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "(Toast\.show|alert|Alert\.alert)" | \
grep -E "text[12]?.*['\"][A-Z][^'\"]*['\"]" | \
grep -v "t(" | \
head -20 > temp_toast_messages.txt

echo "Step 5: Finding button titles and labels..."

# Find button titles and form labels
find . -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" | \
grep -v node_modules | \
grep -v ".git" | \
xargs grep -n -E "(title|label).*=.*['\"][A-Z][^'\"]*['\"]" | \
grep -v "t(" | \
head -20 > temp_button_labels.txt

echo "Step 6: Generating comprehensive report..."

# Combine all findings and generate report
{
    echo "=== COMPREHENSIVE HARD-CODED STRINGS ANALYSIS REPORT ==="
    echo "Generated on: $(date)"
    echo ""
    echo "SUMMARY:"
    echo "--------"
    
    echo ""
    echo "1. JSX TEXT CONTENT (needs t() wrapper):"
    echo "========================================"
    cat temp_jsx_text.txt
    
    echo ""
    echo "2. STRING LITERALS IN PROPS (needs t() wrapper):"
    echo "==============================================="
    cat temp_string_literals.txt
    
    echo ""
    echo "3. VALIDATION ERROR MESSAGES (needs t() wrapper):"
    echo "================================================"
    cat temp_validation_errors.txt
    
    echo ""
    echo "4. TOAST/ALERT MESSAGES (needs t() wrapper):"
    echo "==========================================="
    cat temp_toast_messages.txt
    
    echo ""
    echo "5. BUTTON TITLES AND LABELS (needs t() wrapper):"
    echo "==============================================="
    cat temp_button_labels.txt
    
    echo ""
    echo "PRIORITY RECOMMENDATIONS:"
    echo "========================"
    echo "1. Fix validation error messages first (user-facing errors)"
    echo "2. Fix toast/alert messages (user feedback)"
    echo "3. Fix button titles and labels (navigation elements)"
    echo "4. Fix JSX text content (static text)"
    echo "5. Fix string literals in props (form elements)"
    
} > "$HARDCODED_REPORT"

# Create priority fixes list
{
    echo "=== PRIORITY HARD-CODED STRINGS TO FIX ==="
    echo "HIGH PRIORITY (User-facing errors and feedback):"
    cat temp_validation_errors.txt temp_toast_messages.txt
    echo ""
    echo "MEDIUM PRIORITY (Navigation and form elements):"
    cat temp_button_labels.txt temp_string_literals.txt
    echo ""
    echo "LOW PRIORITY (Static text content):"
    cat temp_jsx_text.txt
} > "$PRIORITY_FIXES"

# Combine all for comprehensive list
cat temp_jsx_text.txt temp_string_literals.txt temp_validation_errors.txt temp_toast_messages.txt temp_button_labels.txt | sort | uniq > "$HARDCODED_STRINGS"

# Clean up temp files
rm -f temp_*.txt

echo ""
echo "Analysis complete!"
echo "Results saved to:"
echo "  - Comprehensive report: $HARDCODED_REPORT"
echo "  - All hard-coded strings: $HARDCODED_STRINGS"
echo "  - Priority fixes: $PRIORITY_FIXES"
echo ""
echo "Total hard-coded strings found: $(wc -l < "$HARDCODED_STRINGS")"
