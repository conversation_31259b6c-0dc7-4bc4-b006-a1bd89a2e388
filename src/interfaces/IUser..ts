import { IChat } from './IChat';

export interface IUserContext {
  user?: IUser;
  setUser: (user: IUser) => void;
  firstLaunch: boolean;
  setFirstLaunch: (firstLaunch: boolean) => void;
}

export interface Chat extends Omit<IChat, 'users'> {
  users: string[];
}

export interface ICoachOfClient {
  _id: string;
  name: string;
  email: string;
  experience: string;
  about: string;
  photo: string;
  code: string;
  referralCode: string;
  userType: 'coach';
  createdAt: string;
  updatedAt: string;
  dob: string;
}

export interface IUser {
  _id: string;
  photos: string[];
  image: string;
  coach?: ICoachOfClient;
  name: string;
  email: string;
  experience: string;
  about: string;
  photo: string;
  code: string;
  verificationCodeExpiresAt: number;
  referralCode?: string;
  dob?: string;
  gender?: string;
  height?: string;
  weight?: string;
  goal?: string;
  step?: number;
  goalWeight?: string;
  howActive?: string;
  createdAt: Date | null;
  updatedAt: Date | null;
  userType: 'coach' | 'client';
  archived: boolean;
  deleted: boolean;
  chats?: Chat[];
}

export interface INutrients {
  usedCalories: number;
  usedProtiens: number;
  usedCarbs: number;
  usedFats: number;
}

export interface IGoal {
  _id: string;
  calories: number;
  proteins: number;
  carbs: number;
  fat: number;
  proteinsPercentage: number;
  carbsPercentage: number;
  fatPercentage: number;
  coach: string;
  client: string;
  createdAt: string;
  updatedAt: string;
}

export interface IUserInfo {
  usedData: INutrients;
  goal: IGoal;
}
