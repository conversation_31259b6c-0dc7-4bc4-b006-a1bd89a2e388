import {IExercise} from './IExercise';

export interface IActivity {
  clientId: string;
  date: Date;
  exercises: ExerciseLog[];
  _id: string;
  totalCaloriesBurned: number;
  totalDuration: number;
}

export interface ICreateActivity {
  clientId: string;
  date: Date;
  exercises: ExerciseLog[];
}

export interface ExerciseLog {
  exercise: string | IExercise;
  duration: number;
  caloriesBurned: number;
}
