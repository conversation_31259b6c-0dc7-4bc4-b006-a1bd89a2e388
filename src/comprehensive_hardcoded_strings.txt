./src/screens/authflow/AddFirstClient/AddFirstClient.tsx:66:          error?.response.data.message === 'A duplicate entry already exists.'
./src/screens/authflow/AddFirstClient/AddFirstClient.tsx:68:          errorMessage = 'This client is already linked to a different coach';
./src/screens/authflow/ClientQuestionare/QuestionareHeader.tsx:44:          <Text style={styles.skipText}>Skip</Text>
./src/screens/authflow/EmailVerification/EmailVerification.tsx:58:        errResp.data?.message === 'Invalid Email'
./src/screens/authflow/OTPVerification/OTPVerification.tsx:68:      if (errResp?.statusCode === 400 && errResp?.message === 'Invalid OTP') {
./src/screens/authflow/PreviousPassword/PreviousPassword.tsx:67:        err.response?.data?.message === 'Old password is incorrect'
./src/screens/authflow/ResetPassword/ResetPassword.tsx:85:        errResp.message === 'Old password is incorrect'
./src/screens/authflow/SignUp/SignUp.tsx:122:      if (errResp.data?.message == 'Email Already exist') {
./src/screens/clientflow/Activity/NewExercise.tsx:121:          label="Sets (optional)"
./src/screens/clientflow/Activity/NewExercise.tsx:129:          label="Reps (optional)"
./src/screens/clientflow/EditGoals/index.tsx:29:  calories: yup.string().required('Calories is required'),
./src/screens/clientflow/EditGoals/index.tsx:30:  proteins: yup.string().required('Proteins is required'),
./src/screens/clientflow/EditGoals/index.tsx:31:  proteinsPercentage: yup.string().required('Proteins % is required'),
./src/screens/clientflow/EditGoals/index.tsx:32:  carbs: yup.string().required('Carbs is required'),
./src/screens/clientflow/EditGoals/index.tsx:33:  carbsPercentage: yup.string().required('Carbs % is required'),
./src/screens/clientflow/EditGoals/index.tsx:34:  fat: yup.string().required('Fats is required'),
./src/screens/clientflow/EditGoals/index.tsx:35:  fatPercentage: yup.string().required('Fat % is required'),
./src/screens/clientflow/MealHistory/index.tsx:414:                  <Text style={{marginTop: 4}}>Liked by coach: </Text>
./src/screens/clientflow/MealHistory/index.tsx:49:  name: Yup.string().required('Food name is required'),
./src/screens/clientflow/MealHistory/index.tsx:50:  quantity: Yup.number().required('Quantity is required'),
./src/screens/clientflow/MealHistory/index.tsx:51:  calories: Yup.number().required('Enter calories'),
./src/screens/clientflow/MealHistory/index.tsx:52:  proteins: Yup.number().required('Enter proteins'),
./src/screens/clientflow/MealHistory/index.tsx:53:  carbs: Yup.number().required('Enter carbs'),
./src/screens/clientflow/MealHistory/index.tsx:54:  fats: Yup.number().required('Enter fats'),
./src/screens/clientflow/PersonalInformation/index.tsx:223:        <Text style={[styles.headingText, {color: 'black'}]}>Health</Text>
./src/screens/homeflow/AddClient/index.tsx:48:        error?.response.data.message === 'A duplicate entry already exists.'
./src/screens/homeflow/ClientDetail/index.tsx:190:        <DetailItem data={details.email} title={'Email'} />
./src/screens/homeflow/ClientDetail/index.tsx:191:        {/* <DetailItem data={ ''} title={'Phone'} /> //TODO: Add phone number to details */}
./src/screens/homeflow/ClientDetail/index.tsx:192:        <DetailItem data={details.dob || ''} title={'Age'} />
./src/screens/homeflow/ClientDetail/index.tsx:193:        <DetailItem data={details.gender || ''} title={'Gender'} />
./src/screens/homeflow/ClientDetail/index.tsx:194:        <DetailItem data={details.goal || ''} title={'Goal'} />
./src/screens/homeflow/ClientDetail/index.tsx:195:        <DetailItem data={details.weight || ''} title={'Current Weight'} />
./src/screens/homeflow/ClientDetail/index.tsx:197:          <DetailItem data={details.goalWeight} title={'Goal Weight'} />
./src/screens/homeflow/ClientDetail/index.tsx:199:        <DetailItem data={details.height || ''} title={'Height'} />
./src/screens/homeflow/ClientDetail/index.tsx:200:        <DetailItem data={details.howActive || ''} title={'Activity Level'} />
./src/screens/homeflow/ClientHistory/index.tsx:137:      .catch(error => console.log('ERR while fetching client details by ID'));
./src/screens/homeflow/EditDocument/index.tsx:25:  title: yup.string().required('Document Name is required.'),
./src/screens/homeflow/EditDocument/index.tsx:26:  description: yup.string().required('Document Description is required.'),
./src/screens/homeflow/PersonalInformation/index.tsx:28:  name: Yup.string().required('Name is required'),
./src/screens/homeflow/PersonalInformation/index.tsx:29:  email: Yup.string().email('Invalid email').required('Email is required'),
./src/screens/homeflow/PersonalInformation/index.tsx:31:  dob: Yup.string().required('Age is required'),
./src/screens/homeflow/PersonalInformation/index.tsx:32:  experience: Yup.string().required('Experience is required'),
./src/screens/homeflow/PersonalInformation/index.tsx:33:  about: Yup.string().required('About is required'),
./src/utilities/useApiHandler.ts:10:    if (error.code === 'ERR_NETWORK') {
./src/utilities/useApiHandler.ts:38:    if (error.code === 'ERR_NETWORK') {
