import AppInstance from "../config/global.axios";

// CLIENT ADD WEIGHT PROGRESS
async function AddWeight(weight: any) {
  const response = await AppInstance({
    url: "/weight/create",
    method: "POST",
    data: weight
  });
  return response;
}

// UPLOAD IMAGE FOR WEIGHT
async function UploadImagesForWeight(payload: any) {
  const response = await AppInstance({
    url: "/upload-image/photos",
    method: "POST",
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: payload
  });
  return response;
}

// GET WEIGHTS OF THE CLIENT (CLIENT AUTH)
async function GetWeight() {
  const response = await AppInstance({
    url: "/weight",
    method: "GET",
  });
  return response;
}

// GET WEIGHTS BY CLIENT ID
async function GetWeightByClient(clientId: string) {
  const response = await AppInstance({
    url: `/weight/client/${clientId}`,
    method: "GET",
  });
  return response;
}

const WeightServices = {
  AddWeight,
  UploadImagesForWeight,
  GetWeight,
  GetWeightByClient
}

export default WeightServices;