import AppInstance from '../config/global.axios';
import { IFood, IReviewFood } from '../interfaces/IFoods';

// GET ALL FOODS FOR A CLIENT
async function GetFoods() {
  const response = await AppInstance({
    url: '/food',
    method: 'GET',
  });
  return response;
}

// CREATE NEW FOOD
async function CreateFood(food: any) {
  const response = await AppInstance({
    url: '/food',
    method: 'POST',
    data: food,
  });
  return response;
}

// UPDATE FOOD
async function UpdateFood(foodId: string, food: Partial<IFood>) {
  const response = await AppInstance({
    url: `/food/${foodId}`,
    method: "PATCH",
    data: food
  });
  return response;
}

// DELETE FOOD FOR CLIENT
async function DeleteFood(foodId: string) {
  const response = await AppInstance({
    url: `/food/${foodId}`,
    method: 'DELETE',
  });
  return response;
}

const FoodServices = {
  GetFoods,
  CreateFood,
  UpdateFood,
  DeleteFood,
};

export default FoodServices;
