// socketService.js
import io, {Socket} from 'socket.io-client';
import {
  appendMessages,
  setChats,
  updateUnreadCounter,
} from '../store/chatsSlice';
import {store} from '../store';
import {IMessage} from 'react-native-gifted-chat';
import moment from 'moment';
import {IQueryResponse} from '../interfaces/QueryResponse';
import {AxiosResponse} from 'axios';
import {IChat} from '../interfaces/IChat';
import ChatServices from './ChatServices';

let socket: Socket | null = null;

export const initializeSocket = (userId: string) => {
  if (!socket) {
    socket = io('https://api.theplatemate.ca', {query: {userId}});

    socket?.on('connect', () => {
      console.log('Socket connected', socket?.id);
    });

    socket.on('newChat', (data: any) => {
      console.log('Socket event received:', data);
      fetchChats();
    });

    socket?.on('newMessage', msg => {
      console.log('New message recieved:', msg);
      const newMessage: IMessage = {
        _id: msg._id,
        createdAt: moment(msg.createdAt).toDate(),
        text: msg.content,
        user: {_id: msg.sender},
      };
      store.dispatch(appendMessages([newMessage]));
    });
  }
};

export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

// @TODO: Need to keep it at some common place we can reuse
const fetchChats = async () => {
  try {
    const resp: AxiosResponse<IQueryResponse<IChat>> =
      await ChatServices.GetChats(20);
    if (resp.status == 200) {
      // SET GLOBAL COUNTER OF UNREAD MESSAGES
      const {results} = resp.data;
      let unreadCount = 0;
      results.forEach(chat => {
        if (chat.unReadMsgCount && chat.lastMessage.sender !== user._id) {
          unreadCount += chat.unReadMsgCount;
        }
      });
      store.dispatch(setChats(resp.data));
      store.dispatch(updateUnreadCounter({counter: unreadCount}));
    }
  } catch (error) {
    console.error(error);
  }
};
